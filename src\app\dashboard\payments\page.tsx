'use client'

import { useEffect, useState } from 'react'
import { Plus, Edit, Trash2, CreditCard, Calendar, AlertCircle } from 'lucide-react'
import { supabase, Payment } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import PaymentForm from '@/components/Forms/PaymentForm'

export default function PaymentsPage() {
  const { user } = useAuth()
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingPayment, setEditingPayment] = useState<Payment | null>(null)
  const [filter, setFilter] = useState<'all' | 'pending' | 'paid' | 'overdue'>('all')

  useEffect(() => {
    if (user) {
      fetchPayments()
    }
  }, [user])

  const fetchPayments = async () => {
    try {
      const { data, error } = await supabase
        .from('payments')
        .select(`
          *,
          rental_agreement:rental_agreements(
            monthly_rent,
            property:properties(name, address),
            tenant:tenants(name, phone)
          )
        `)
        .eq('user_id', user?.id)
        .order('due_date', { ascending: true })

      if (error) throw error
      setPayments(data || [])
    } catch (error) {
      console.error('Error fetching payments:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذه الدفعة؟')) return

    try {
      const { error } = await supabase
        .from('payments')
        .delete()
        .eq('id', id)

      if (error) throw error
      setPayments(payments.filter(p => p.id !== id))
    } catch (error) {
      console.error('Error deleting payment:', error)
      alert('حدث خطأ أثناء حذف الدفعة')
    }
  }

  const getStatusColor = (status: string, dueDate: string) => {
    if (status === 'paid') return 'bg-green-100 text-green-800'
    if (status === 'pending' && new Date(dueDate) < new Date()) return 'bg-red-100 text-red-800'
    if (status === 'pending') return 'bg-yellow-100 text-yellow-800'
    return 'bg-gray-100 text-gray-800'
  }

  const getStatusText = (status: string, dueDate: string) => {
    if (status === 'paid') return 'مدفوع'
    if (status === 'pending' && new Date(dueDate) < new Date()) return 'متأخر'
    if (status === 'pending') return 'معلق'
    return 'ملغي'
  }

  const filteredPayments = payments.filter(payment => {
    if (filter === 'all') return true
    if (filter === 'paid') return payment.status === 'paid'
    if (filter === 'pending') return payment.status === 'pending' && new Date(payment.due_date) >= new Date()
    if (filter === 'overdue') return payment.status === 'pending' && new Date(payment.due_date) < new Date()
    return true
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">إدارة المدفوعات</h1>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors flex items-center"
        >
          <Plus className="w-5 h-5 ml-2" />
          إضافة دفعة جديدة
        </button>
      </div>

      {/* Filter Tabs */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex space-x-4 space-x-reverse">
          {[
            { key: 'all', label: 'الكل', count: payments.length },
            { key: 'pending', label: 'معلقة', count: payments.filter(p => p.status === 'pending' && new Date(p.due_date) >= new Date()).length },
            { key: 'overdue', label: 'متأخرة', count: payments.filter(p => p.status === 'pending' && new Date(p.due_date) < new Date()).length },
            { key: 'paid', label: 'مدفوعة', count: payments.filter(p => p.status === 'paid').length },
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setFilter(tab.key as any)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                filter === tab.key
                  ? 'bg-primary-100 text-primary-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>
      </div>

      {filteredPayments.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <CreditCard className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مدفوعات</h3>
          <p className="text-gray-600 mb-4">ابدأ بإضافة دفعة جديدة</p>
          <button
            onClick={() => setShowAddForm(true)}
            className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
          >
            إضافة دفعة جديدة
          </button>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    العقار / المستأجر
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المبلغ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    تاريخ الاستحقاق
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    تاريخ الدفع
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredPayments.map((payment) => (
                  <tr key={payment.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {payment.rental_agreement?.property?.name || 'عقار غير محدد'}
                        </div>
                        <div className="text-sm text-gray-600">
                          {payment.rental_agreement?.tenant?.name || 'مستأجر غير محدد'}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-semibold text-gray-900">
                        {payment.amount.toLocaleString()} ريال
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900">
                        <Calendar className="w-4 h-4 ml-1" />
                        {new Date(payment.due_date).toLocaleDateString('ar-SA')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {payment.payment_date ? new Date(payment.payment_date).toLocaleDateString('ar-SA') : '-'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(payment.status, payment.due_date)}`}>
                        {getStatusText(payment.status, payment.due_date)}
                        {payment.status === 'pending' && new Date(payment.due_date) < new Date() && (
                          <AlertCircle className="w-3 h-3 mr-1" />
                        )}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2 space-x-reverse">
                        <button
                          onClick={() => setEditingPayment(payment)}
                          className="text-primary-600 hover:text-primary-900"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(payment.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Add/Edit Payment Modal */}
      {(showAddForm || editingPayment) && (
        <PaymentForm
          payment={editingPayment}
          onSuccess={() => {
            setShowAddForm(false)
            setEditingPayment(null)
            fetchPayments()
          }}
          onCancel={() => {
            setShowAddForm(false)
            setEditingPayment(null)
          }}
        />
      )}
    </div>
  )
}
