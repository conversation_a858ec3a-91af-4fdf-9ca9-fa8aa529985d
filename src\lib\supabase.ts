import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnon<PERSON>ey)

// Database types
export interface Property {
  id: string
  user_id: string
  name: string
  address: string
  property_type: string
  area?: number
  rooms?: number
  bathrooms?: number
  description?: string
  purchase_price?: number
  current_value?: number
  status: string
  created_at: string
  updated_at: string
}

export interface Tenant {
  id: string
  user_id: string
  name: string
  phone?: string
  email?: string
  national_id?: string
  emergency_contact?: string
  emergency_phone?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface RentalAgreement {
  id: string
  user_id: string
  property_id: string
  tenant_id: string
  start_date: string
  end_date: string
  monthly_rent: number
  security_deposit?: number
  status: string
  contract_terms?: string
  created_at: string
  updated_at: string
  property?: Property
  tenant?: Tenant
}

export interface Payment {
  id: string
  user_id: string
  rental_agreement_id: string
  amount: number
  payment_date: string
  due_date: string
  payment_method?: string
  status: string
  notes?: string
  created_at: string
  updated_at: string
  rental_agreement?: RentalAgreement
}

export interface Expense {
  id: string
  user_id: string
  property_id?: string
  category: string
  description: string
  amount: number
  expense_date: string
  receipt_url?: string
  created_at: string
  updated_at: string
  property?: Property
}
