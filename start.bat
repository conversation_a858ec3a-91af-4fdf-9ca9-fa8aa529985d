@echo off
echo ========================================
echo    نظام إدارة العقارات والإيجارات
echo ========================================
echo.

echo التحقق من تثبيت Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت!
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    echo ثم أعد تشغيل هذا الملف
    pause
    exit /b 1
)

echo Node.js مثبت بنجاح ✓
echo.

echo التحقق من ملف البيئة...
if not exist ".env.local" (
    echo تحذير: ملف .env.local غير موجود!
    echo يرجى نسخ .env.local.example إلى .env.local وإضافة مفاتيح Supabase
    echo راجع ملف SETUP.md للتفاصيل
    pause
)

echo تثبيت التبعيات...
npm install

if %errorlevel% neq 0 (
    echo خطأ في تثبيت التبعيات!
    pause
    exit /b 1
)

echo.
echo بدء تشغيل الخادم...
echo سيتم فتح التطبيق على: http://localhost:3000
echo.
echo للإيقاف: اضغط Ctrl+C
echo.

npm run dev

pause
