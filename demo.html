<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العقارات والإيجارات - عرض تجريبي</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', system-ui, sans-serif;
            direction: rtl;
        }
        .sidebar {
            position: fixed;
            right: 0;
            top: 0;
            height: 100vh;
            width: 256px;
            background: white;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 50;
        }
        .main-content {
            margin-right: 256px;
            min-height: 100vh;
            background: #f9fafb;
        }
        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            margin: 4px 16px;
            border-radius: 8px;
            color: #6b7280;
            text-decoration: none;
            transition: all 0.2s;
        }
        .nav-item:hover {
            background: #f3f4f6;
            color: #374151;
        }
        .nav-item.active {
            background: #dbeafe;
            color: #1d4ed8;
            border-right: 4px solid #2563eb;
        }
        .nav-item i {
            margin-left: 12px;
            width: 20px;
            height: 20px;
        }
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            padding: 24px;
            margin-bottom: 24px;
        }
        .stat-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            padding: 24px;
            border: 1px solid #e5e7eb;
        }
        .btn-primary {
            background: #2563eb;
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: background 0.2s;
        }
        .btn-primary:hover {
            background: #1d4ed8;
        }
        .property-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 16px;
            border: 1px solid #e5e7eb;
        }
        .status-available { background: #dcfce7; color: #166534; }
        .status-rented { background: #dbeafe; color: #1e40af; }
        .status-maintenance { background: #fef3c7; color: #92400e; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="flex items-center justify-center h-16 px-4 bg-blue-600">
            <h1 class="text-xl font-bold text-white">إدارة العقارات</h1>
        </div>
        
        <nav class="flex-1 px-4 py-6 space-y-2">
            <a href="#dashboard" class="nav-item active" onclick="showPage('dashboard')">
                <i data-lucide="home"></i>
                لوحة التحكم
            </a>
            <a href="#properties" class="nav-item" onclick="showPage('properties')">
                <i data-lucide="building"></i>
                العقارات
            </a>
            <a href="#tenants" class="nav-item" onclick="showPage('tenants')">
                <i data-lucide="users"></i>
                المستأجرين
            </a>
            <a href="#rentals" class="nav-item" onclick="showPage('rentals')">
                <i data-lucide="file-text"></i>
                عقود الإيجار
            </a>
            <a href="#payments" class="nav-item" onclick="showPage('payments')">
                <i data-lucide="credit-card"></i>
                المدفوعات
            </a>
            <a href="#expenses" class="nav-item" onclick="showPage('expenses')">
                <i data-lucide="receipt"></i>
                المصروفات
            </a>
            <a href="#reports" class="nav-item" onclick="showPage('reports')">
                <i data-lucide="trending-up"></i>
                التقارير المالية
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Dashboard Page -->
        <div id="dashboard" class="page p-6">
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-3xl font-bold text-gray-900">لوحة التحكم</h1>
                <p class="text-gray-600">مرحباً بك في نظام إدارة العقارات</p>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="stat-card bg-blue-50 border-blue-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">إجمالي العقارات</p>
                            <p class="text-2xl font-bold text-blue-600 mt-1">12</p>
                        </div>
                        <i data-lucide="building" class="w-8 h-8 text-blue-600"></i>
                    </div>
                </div>
                
                <div class="stat-card bg-green-50 border-green-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">إجمالي المستأجرين</p>
                            <p class="text-2xl font-bold text-green-600 mt-1">8</p>
                        </div>
                        <i data-lucide="users" class="w-8 h-8 text-green-600"></i>
                    </div>
                </div>
                
                <div class="stat-card bg-purple-50 border-purple-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">عقود الإيجار النشطة</p>
                            <p class="text-2xl font-bold text-purple-600 mt-1">7</p>
                        </div>
                        <i data-lucide="file-text" class="w-8 h-8 text-purple-600"></i>
                    </div>
                </div>
                
                <div class="stat-card bg-yellow-50 border-yellow-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">الدخل الشهري</p>
                            <p class="text-2xl font-bold text-yellow-600 mt-1">45,000 ريال</p>
                        </div>
                        <i data-lucide="trending-up" class="w-8 h-8 text-yellow-600"></i>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">إجراءات سريعة</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-right" onclick="showPage('properties')">
                        <i data-lucide="building" class="w-8 h-8 text-blue-600 mb-2"></i>
                        <h3 class="font-medium text-gray-900">إضافة عقار جديد</h3>
                        <p class="text-sm text-gray-600 mt-1">أضف عقار جديد إلى محفظتك</p>
                    </button>
                    <button class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-right" onclick="showPage('tenants')">
                        <i data-lucide="users" class="w-8 h-8 text-green-600 mb-2"></i>
                        <h3 class="font-medium text-gray-900">إضافة مستأجر جديد</h3>
                        <p class="text-sm text-gray-600 mt-1">سجل مستأجر جديد في النظام</p>
                    </button>
                    <button class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-right" onclick="showPage('rentals')">
                        <i data-lucide="file-text" class="w-8 h-8 text-purple-600 mb-2"></i>
                        <h3 class="font-medium text-gray-900">إنشاء عقد إيجار</h3>
                        <p class="text-sm text-gray-600 mt-1">أنشئ عقد إيجار جديد</p>
                    </button>
                </div>
            </div>
        </div>

        <!-- Properties Page -->
        <div id="properties" class="page p-6" style="display: none;">
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-3xl font-bold text-gray-900">إدارة العقارات</h1>
                <button class="btn-primary flex items-center">
                    <i data-lucide="plus" class="w-5 h-5 ml-2"></i>
                    إضافة عقار جديد
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="property-card">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">شقة الياسمين</h3>
                        <span class="status-rented px-2 py-1 text-xs font-semibold rounded-full">مؤجر</span>
                    </div>
                    <p class="text-gray-600 mb-2">حي الياسمين، الرياض</p>
                    <p class="text-sm text-gray-500 mb-3">3 غرف • 2 حمام • 120 م²</p>
                    <div class="flex items-center justify-between">
                        <span class="text-lg font-bold text-green-600">3,500 ريال/شهر</span>
                        <div class="flex space-x-2 space-x-reverse">
                            <button class="text-blue-600 hover:text-blue-800">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="property-card">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">فيلا النرجس</h3>
                        <span class="status-available px-2 py-1 text-xs font-semibold rounded-full">متاح</span>
                    </div>
                    <p class="text-gray-600 mb-2">حي النرجس، الرياض</p>
                    <p class="text-sm text-gray-500 mb-3">5 غرف • 4 حمام • 300 م²</p>
                    <div class="flex items-center justify-between">
                        <span class="text-lg font-bold text-green-600">8,000 ريال/شهر</span>
                        <div class="flex space-x-2 space-x-reverse">
                            <button class="text-blue-600 hover:text-blue-800">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="property-card">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">محل تجاري</h3>
                        <span class="status-maintenance px-2 py-1 text-xs font-semibold rounded-full">تحت الصيانة</span>
                    </div>
                    <p class="text-gray-600 mb-2">شارع الملك فهد، الرياض</p>
                    <p class="text-sm text-gray-500 mb-3">محل • 80 م²</p>
                    <div class="flex items-center justify-between">
                        <span class="text-lg font-bold text-green-600">4,500 ريال/شهر</span>
                        <div class="flex space-x-2 space-x-reverse">
                            <button class="text-blue-600 hover:text-blue-800">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Other pages would be similar... -->
        <div id="tenants" class="page p-6" style="display: none;">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">إدارة المستأجرين</h1>
            <div class="card">
                <p class="text-gray-600">صفحة إدارة المستأجرين - سيتم عرض قائمة المستأجرين هنا</p>
            </div>
        </div>

        <div id="rentals" class="page p-6" style="display: none;">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">عقود الإيجار</h1>
            <div class="card">
                <p class="text-gray-600">صفحة عقود الإيجار - سيتم عرض العقود هنا</p>
            </div>
        </div>

        <div id="payments" class="page p-6" style="display: none;">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">إدارة المدفوعات</h1>
            <div class="card">
                <p class="text-gray-600">صفحة المدفوعات - سيتم عرض المدفوعات هنا</p>
            </div>
        </div>

        <div id="expenses" class="page p-6" style="display: none;">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">إدارة المصروفات</h1>
            <div class="card">
                <p class="text-gray-600">صفحة المصروفات - سيتم عرض المصروفات هنا</p>
            </div>
        </div>

        <div id="reports" class="page p-6" style="display: none;">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">التقارير المالية</h1>
            <div class="card">
                <p class="text-gray-600">صفحة التقارير - سيتم عرض التقارير المالية هنا</p>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Page navigation
        function showPage(pageId) {
            // Hide all pages
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.style.display = 'none');
            
            // Show selected page
            document.getElementById(pageId).style.display = 'block';
            
            // Update navigation
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            
            const activeItem = document.querySelector(`[href="#${pageId}"]`);
            if (activeItem) {
                activeItem.classList.add('active');
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons();
        });
    </script>
</body>
</html>
