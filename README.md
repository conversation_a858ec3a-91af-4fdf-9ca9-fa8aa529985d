# نظام إدارة العقارات والإيجارات

نظام شامل لإدارة العقارات والإيجارات والمستأجرين مع واجهة عربية كاملة ودعم RTL.

## المميزات الرئيسية

### 🏢 إدارة العقارات
- إضافة وتعديل وحذف العقارات
- تتبع تفاصيل العقار (المساحة، عدد الغرف، النوع، إلخ)
- إدارة حالة العقار (متاح، مؤجر، تحت الصيانة)
- ربط العقارات بالمصروفات

### 👥 إدارة المستأجرين
- قاعدة بيانات شاملة للمستأجرين
- معلومات الاتصال وبيانات الطوارئ
- تتبع تاريخ المستأجرين
- ملاحظات وتفاصيل إضافية

### 📋 عقود الإيجار
- إنشاء وإدارة عقود الإيجار
- ربط العقارات بالمستأجرين
- تحديد فترات الإيجار والمبالغ
- تتبع حالة العقود (نشط، منتهي، مُنهى)

### 💰 إدارة المدفوعات
- تتبع مدفوعات الإيجار
- إدارة المدفوعات المعلقة والمتأخرة
- طرق دفع متعددة
- تواريخ الاستحقاق والدفع

### 🧾 إدارة المصروفات
- تسجيل مصروفات العقارات
- تصنيف المصروفات (صيانة، كهرباء، مياه، إلخ)
- ربط المصروفات بعقارات محددة
- رفع إيصالات المصروفات

### 📊 التقارير المالية
- تقارير الإيرادات والمصروفات
- حساب صافي الربح وهامش الربح
- تتبع المدفوعات المعلقة والمتأخرة
- إحصائيات شاملة للأداء المالي

## التقنيات المستخدمة

### Frontend
- **Next.js 14** - إطار عمل React مع App Router
- **TypeScript** - للتطوير الآمن والموثوق
- **Tailwind CSS** - للتصميم مع دعم RTL كامل
- **Lucide React** - مكتبة الأيقونات

### Backend & Database
- **Supabase** - قاعدة بيانات PostgreSQL مع المصادقة
- **Row Level Security (RLS)** - لحماية البيانات
- **Real-time subscriptions** - للتحديثات الفورية

### المصادقة والأمان
- **Supabase Auth** - نظام مصادقة آمن
- **JWT Tokens** - للجلسات الآمنة
- **RLS Policies** - لضمان عدم وصول المستخدمين لبيانات الآخرين

## التثبيت والتشغيل

### المتطلبات
- Node.js 18+ 
- npm أو yarn
- حساب Supabase

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone [repository-url]
cd real-estate-management
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **إعداد متغيرات البيئة**
```bash
cp .env.local.example .env.local
```

أضف متغيرات Supabase في `.env.local`:
```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. **تشغيل المشروع**
```bash
npm run dev
```

5. **فتح التطبيق**
افتح [http://localhost:3000](http://localhost:3000) في المتصفح

## إعداد قاعدة البيانات

### الجداول المطلوبة
- `properties` - العقارات
- `tenants` - المستأجرين  
- `rental_agreements` - عقود الإيجار
- `payments` - المدفوعات
- `expenses` - المصروفات

### سياسات الأمان (RLS)
تم تطبيق سياسات Row Level Security لضمان:
- وصول كل مستخدم لبياناته فقط
- حماية البيانات من الوصول غير المصرح به
- تشفير الاتصالات

## الاستخدام

### إنشاء حساب جديد
1. انتقل إلى صفحة التسجيل
2. أدخل البريد الإلكتروني وكلمة المرور
3. تحقق من البريد الإلكتروني لتفعيل الحساب

### إضافة عقار جديد
1. انتقل إلى صفحة العقارات
2. اضغط "إضافة عقار جديد"
3. املأ تفاصيل العقار
4. احفظ البيانات

### إنشاء عقد إيجار
1. تأكد من وجود عقار ومستأجر
2. انتقل إلى صفحة عقود الإيجار
3. اضغط "إنشاء عقد جديد"
4. اختر العقار والمستأجر
5. حدد تفاصيل العقد

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. تطبيق التغييرات مع الاختبارات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم:
- افتح issue جديد في GitHub
- راجع الوثائق
- تواصل مع فريق التطوير

---

تم تطوير هذا النظام بعناية لتلبية احتياجات إدارة العقارات في المنطقة العربية مع دعم كامل للغة العربية واتجاه RTL.
