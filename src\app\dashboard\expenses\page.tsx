'use client'

import { useEffect, useState } from 'react'
import { Plus, Edit, Trash2, Receipt, Calendar, DollarSign } from 'lucide-react'
import { supabase, Expense } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import ExpenseForm from '@/components/Forms/ExpenseForm'

export default function ExpensesPage() {
  const { user } = useAuth()
  const [expenses, setExpenses] = useState<Expense[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingExpense, setEditingExpense] = useState<Expense | null>(null)
  const [filter, setFilter] = useState<string>('all')

  useEffect(() => {
    if (user) {
      fetchExpenses()
    }
  }, [user])

  const fetchExpenses = async () => {
    try {
      const { data, error } = await supabase
        .from('expenses')
        .select(`
          *,
          property:properties(name, address)
        `)
        .eq('user_id', user?.id)
        .order('expense_date', { ascending: false })

      if (error) throw error
      setExpenses(data || [])
    } catch (error) {
      console.error('Error fetching expenses:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا المصروف؟')) return

    try {
      const { error } = await supabase
        .from('expenses')
        .delete()
        .eq('id', id)

      if (error) throw error
      setExpenses(expenses.filter(e => e.id !== id))
    } catch (error) {
      console.error('Error deleting expense:', error)
      alert('حدث خطأ أثناء حذف المصروف')
    }
  }

  const categories = [
    'صيانة',
    'كهرباء',
    'مياه',
    'تأمين',
    'ضرائب',
    'تسويق',
    'إدارة',
    'أخرى'
  ]

  const filteredExpenses = expenses.filter(expense => {
    if (filter === 'all') return true
    return expense.category === filter
  })

  const totalExpenses = filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0)

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">إدارة المصروفات</h1>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors flex items-center"
        >
          <Plus className="w-5 h-5 ml-2" />
          إضافة مصروف جديد
        </button>
      </div>

      {/* Summary Card */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">إجمالي المصروفات</h3>
            <p className="text-3xl font-bold text-red-600 mt-2">
              {totalExpenses.toLocaleString()} ريال
            </p>
          </div>
          <DollarSign className="w-12 h-12 text-red-600" />
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              filter === 'all'
                ? 'bg-primary-100 text-primary-700'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            }`}
          >
            الكل ({expenses.length})
          </button>
          {categories.map((category) => {
            const count = expenses.filter(e => e.category === category).length
            return (
              <button
                key={category}
                onClick={() => setFilter(category)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  filter === category
                    ? 'bg-primary-100 text-primary-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                {category} ({count})
              </button>
            )
          })}
        </div>
      </div>

      {filteredExpenses.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <Receipt className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مصروفات</h3>
          <p className="text-gray-600 mb-4">ابدأ بإضافة مصروف جديد</p>
          <button
            onClick={() => setShowAddForm(true)}
            className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
          >
            إضافة مصروف جديد
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredExpenses.map((expense) => (
            <div key={expense.id} className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <Receipt className="w-6 h-6 text-red-600 ml-2" />
                  <span className="text-sm font-medium text-gray-600 bg-gray-100 px-2 py-1 rounded">
                    {expense.category}
                  </span>
                </div>
                <div className="flex space-x-2 space-x-reverse">
                  <button
                    onClick={() => setEditingExpense(expense)}
                    className="text-primary-600 hover:text-primary-900 p-1"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(expense.id)}
                    className="text-red-600 hover:text-red-900 p-1"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{expense.description}</h3>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">المبلغ:</span>
                  <span className="text-lg font-bold text-red-600">
                    {expense.amount.toLocaleString()} ريال
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">التاريخ:</span>
                  <div className="flex items-center text-sm text-gray-900">
                    <Calendar className="w-4 h-4 ml-1" />
                    {new Date(expense.expense_date).toLocaleDateString('ar-SA')}
                  </div>
                </div>

                {expense.property && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-600">العقار:</span>
                    <span className="text-sm text-gray-900">{expense.property.name}</span>
                  </div>
                )}

                {expense.receipt_url && (
                  <div className="pt-3 border-t border-gray-200">
                    <a
                      href={expense.receipt_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                    >
                      عرض الإيصال
                    </a>
                  </div>
                )}
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200">
                <span className="text-xs text-gray-500">
                  تم الإضافة: {new Date(expense.created_at).toLocaleDateString('ar-SA')}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add/Edit Expense Modal */}
      {(showAddForm || editingExpense) && (
        <ExpenseForm
          expense={editingExpense}
          onSuccess={() => {
            setShowAddForm(false)
            setEditingExpense(null)
            fetchExpenses()
          }}
          onCancel={() => {
            setShowAddForm(false)
            setEditingExpense(null)
          }}
        />
      )}
    </div>
  )
}
