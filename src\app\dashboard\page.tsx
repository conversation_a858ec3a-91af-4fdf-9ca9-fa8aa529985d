'use client'

import { useEffect, useState } from 'react'
import { Building, Users, FileText, TrendingUp } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'

interface DashboardStats {
  totalProperties: number
  totalTenants: number
  activeRentals: number
  monthlyIncome: number
  pendingPayments: number
}

export default function DashboardPage() {
  const { user } = useAuth()
  const [stats, setStats] = useState<DashboardStats>({
    totalProperties: 0,
    totalTenants: 0,
    activeRentals: 0,
    monthlyIncome: 0,
    pendingPayments: 0,
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      fetchDashboardStats()
    }
  }, [user])

  const fetchDashboardStats = async () => {
    try {
      // Fetch properties count
      const { count: propertiesCount } = await supabase
        .from('properties')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user?.id)

      // Fetch tenants count
      const { count: tenantsCount } = await supabase
        .from('tenants')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user?.id)

      // Fetch active rentals count
      const { count: activeRentalsCount } = await supabase
        .from('rental_agreements')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user?.id)
        .eq('status', 'active')

      // Fetch monthly income (sum of active rental agreements)
      const { data: activeRentals } = await supabase
        .from('rental_agreements')
        .select('monthly_rent')
        .eq('user_id', user?.id)
        .eq('status', 'active')

      const monthlyIncome = activeRentals?.reduce((sum, rental) => sum + (rental.monthly_rent || 0), 0) || 0

      // Fetch pending payments count
      const { count: pendingPaymentsCount } = await supabase
        .from('payments')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user?.id)
        .eq('status', 'pending')

      setStats({
        totalProperties: propertiesCount || 0,
        totalTenants: tenantsCount || 0,
        activeRentals: activeRentalsCount || 0,
        monthlyIncome,
        pendingPayments: pendingPaymentsCount || 0,
      })
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const statCards = [
    {
      title: 'إجمالي العقارات',
      value: stats.totalProperties,
      icon: Building,
      color: 'bg-blue-500',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-600',
    },
    {
      title: 'إجمالي المستأجرين',
      value: stats.totalTenants,
      icon: Users,
      color: 'bg-green-500',
      bgColor: 'bg-green-50',
      textColor: 'text-green-600',
    },
    {
      title: 'عقود الإيجار النشطة',
      value: stats.activeRentals,
      icon: FileText,
      color: 'bg-purple-500',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-600',
    },
    {
      title: 'الدخل الشهري',
      value: `${stats.monthlyIncome.toLocaleString()} ريال`,
      icon: TrendingUp,
      color: 'bg-yellow-500',
      bgColor: 'bg-yellow-50',
      textColor: 'text-yellow-600',
    },
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">لوحة التحكم</h1>
        <p className="text-gray-600">مرحباً بك في نظام إدارة العقارات</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => (
          <div key={index} className={`${card.bgColor} rounded-lg p-6 border border-gray-200`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{card.title}</p>
                <p className={`text-2xl font-bold ${card.textColor} mt-1`}>
                  {card.value}
                </p>
              </div>
              <div className={`${card.color} p-3 rounded-lg`}>
                <card.icon className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">إجراءات سريعة</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-right">
            <Building className="w-8 h-8 text-blue-600 mb-2" />
            <h3 className="font-medium text-gray-900">إضافة عقار جديد</h3>
            <p className="text-sm text-gray-600 mt-1">أضف عقار جديد إلى محفظتك</p>
          </button>
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-right">
            <Users className="w-8 h-8 text-green-600 mb-2" />
            <h3 className="font-medium text-gray-900">إضافة مستأجر جديد</h3>
            <p className="text-sm text-gray-600 mt-1">سجل مستأجر جديد في النظام</p>
          </button>
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-right">
            <FileText className="w-8 h-8 text-purple-600 mb-2" />
            <h3 className="font-medium text-gray-900">إنشاء عقد إيجار</h3>
            <p className="text-sm text-gray-600 mt-1">أنشئ عقد إيجار جديد</p>
          </button>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">النشاط الأخير</h2>
        <div className="space-y-3">
          <div className="flex items-center justify-between py-3 border-b border-gray-100">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full ml-3"></div>
              <span className="text-gray-900">تم إضافة عقار جديد</span>
            </div>
            <span className="text-sm text-gray-500">منذ ساعتين</span>
          </div>
          <div className="flex items-center justify-between py-3 border-b border-gray-100">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full ml-3"></div>
              <span className="text-gray-900">تم استلام دفعة إيجار</span>
            </div>
            <span className="text-sm text-gray-500">أمس</span>
          </div>
          <div className="flex items-center justify-between py-3">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-yellow-500 rounded-full ml-3"></div>
              <span className="text-gray-900">تم تجديد عقد إيجار</span>
            </div>
            <span className="text-sm text-gray-500">منذ 3 أيام</span>
          </div>
        </div>
      </div>
    </div>
  )
}
