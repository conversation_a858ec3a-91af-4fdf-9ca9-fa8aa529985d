'use client'

import { useEffect, useState } from 'react'
import { TrendingUp, TrendingDown, DollarSign, Calendar, FileText, Download } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'

interface FinancialData {
  totalIncome: number
  totalExpenses: number
  netProfit: number
  monthlyIncome: number
  pendingPayments: number
  overduePayments: number
  monthlyData: Array<{
    month: string
    income: number
    expenses: number
    profit: number
  }>
}

export default function ReportsPage() {
  const { user } = useAuth()
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('current-year')
  const [financialData, setFinancialData] = useState<FinancialData>({
    totalIncome: 0,
    totalExpenses: 0,
    netProfit: 0,
    monthlyIncome: 0,
    pendingPayments: 0,
    overduePayments: 0,
    monthlyData: []
  })

  useEffect(() => {
    if (user) {
      fetchFinancialData()
    }
  }, [user, selectedPeriod])

  const fetchFinancialData = async () => {
    try {
      setLoading(true)
      
      // Get date range based on selected period
      const now = new Date()
      let startDate: Date
      let endDate = now

      switch (selectedPeriod) {
        case 'current-month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1)
          break
        case 'last-month':
          startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1)
          endDate = new Date(now.getFullYear(), now.getMonth(), 0)
          break
        case 'current-year':
          startDate = new Date(now.getFullYear(), 0, 1)
          break
        case 'last-year':
          startDate = new Date(now.getFullYear() - 1, 0, 1)
          endDate = new Date(now.getFullYear() - 1, 11, 31)
          break
        default:
          startDate = new Date(now.getFullYear(), 0, 1)
      }

      // Fetch paid payments (income)
      const { data: paidPayments, error: paymentsError } = await supabase
        .from('payments')
        .select('amount, payment_date')
        .eq('user_id', user?.id)
        .eq('status', 'paid')
        .gte('payment_date', startDate.toISOString().split('T')[0])
        .lte('payment_date', endDate.toISOString().split('T')[0])

      if (paymentsError) throw paymentsError

      // Fetch expenses
      const { data: expenses, error: expensesError } = await supabase
        .from('expenses')
        .select('amount, expense_date')
        .eq('user_id', user?.id)
        .gte('expense_date', startDate.toISOString().split('T')[0])
        .lte('expense_date', endDate.toISOString().split('T')[0])

      if (expensesError) throw expensesError

      // Calculate totals
      const totalIncome = paidPayments?.reduce((sum, payment) => sum + payment.amount, 0) || 0
      const totalExpenses = expenses?.reduce((sum, expense) => sum + expense.amount, 0) || 0
      const netProfit = totalIncome - totalExpenses

      // Get monthly income from active rentals
      const { data: activeRentals } = await supabase
        .from('rental_agreements')
        .select('monthly_rent')
        .eq('user_id', user?.id)
        .eq('status', 'active')

      const monthlyIncome = activeRentals?.reduce((sum, rental) => sum + rental.monthly_rent, 0) || 0

      // Get pending and overdue payments
      const { data: pendingPayments } = await supabase
        .from('payments')
        .select('amount, due_date')
        .eq('user_id', user?.id)
        .eq('status', 'pending')

      const pending = pendingPayments?.filter(p => new Date(p.due_date) >= new Date()).reduce((sum, p) => sum + p.amount, 0) || 0
      const overdue = pendingPayments?.filter(p => new Date(p.due_date) < new Date()).reduce((sum, p) => sum + p.amount, 0) || 0

      setFinancialData({
        totalIncome,
        totalExpenses,
        netProfit,
        monthlyIncome,
        pendingPayments: pending,
        overduePayments: overdue,
        monthlyData: [] // We'll implement this later
      })

    } catch (error) {
      console.error('Error fetching financial data:', error)
    } finally {
      setLoading(false)
    }
  }

  const summaryCards = [
    {
      title: 'إجمالي الإيرادات',
      value: `${financialData.totalIncome.toLocaleString()} ريال`,
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      title: 'إجمالي المصروفات',
      value: `${financialData.totalExpenses.toLocaleString()} ريال`,
      icon: TrendingDown,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200'
    },
    {
      title: 'صافي الربح',
      value: `${financialData.netProfit.toLocaleString()} ريال`,
      icon: DollarSign,
      color: financialData.netProfit >= 0 ? 'text-green-600' : 'text-red-600',
      bgColor: financialData.netProfit >= 0 ? 'bg-green-50' : 'bg-red-50',
      borderColor: financialData.netProfit >= 0 ? 'border-green-200' : 'border-red-200'
    },
    {
      title: 'الدخل الشهري المتوقع',
      value: `${financialData.monthlyIncome.toLocaleString()} ريال`,
      icon: Calendar,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">التقارير المالية</h1>
        <div className="flex space-x-3 space-x-reverse">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="current-month">الشهر الحالي</option>
            <option value="last-month">الشهر الماضي</option>
            <option value="current-year">السنة الحالية</option>
            <option value="last-year">السنة الماضية</option>
          </select>
          <button className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors flex items-center">
            <Download className="w-4 h-4 ml-2" />
            تصدير التقرير
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {summaryCards.map((card, index) => (
          <div key={index} className={`${card.bgColor} ${card.borderColor} border rounded-lg p-6`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{card.title}</p>
                <p className={`text-2xl font-bold ${card.color} mt-1`}>
                  {card.value}
                </p>
              </div>
              <card.icon className={`w-8 h-8 ${card.color}`} />
            </div>
          </div>
        ))}
      </div>

      {/* Pending and Overdue Payments */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">المدفوعات المعلقة</h3>
          <div className="text-3xl font-bold text-yellow-600">
            {financialData.pendingPayments.toLocaleString()} ريال
          </div>
          <p className="text-sm text-gray-600 mt-2">مدفوعات مستحقة لم يتم دفعها بعد</p>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">المدفوعات المتأخرة</h3>
          <div className="text-3xl font-bold text-red-600">
            {financialData.overduePayments.toLocaleString()} ريال
          </div>
          <p className="text-sm text-gray-600 mt-2">مدفوعات متأخرة عن موعد الاستحقاق</p>
        </div>
      </div>

      {/* Profit Margin */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">هامش الربح</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-gray-600">الإيرادات</span>
            <span className="font-semibold text-green-600">
              {financialData.totalIncome.toLocaleString()} ريال
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-600">المصروفات</span>
            <span className="font-semibold text-red-600">
              -{financialData.totalExpenses.toLocaleString()} ريال
            </span>
          </div>
          <hr className="border-gray-200" />
          <div className="flex items-center justify-between text-lg">
            <span className="font-semibold text-gray-900">صافي الربح</span>
            <span className={`font-bold ${financialData.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {financialData.netProfit.toLocaleString()} ريال
            </span>
          </div>
          {financialData.totalIncome > 0 && (
            <div className="text-sm text-gray-600">
              هامش الربح: {((financialData.netProfit / financialData.totalIncome) * 100).toFixed(1)}%
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-right">
            <FileText className="w-6 h-6 text-blue-600 mb-2" />
            <h4 className="font-medium text-gray-900">إضافة مصروف</h4>
            <p className="text-sm text-gray-600 mt-1">سجل مصروف جديد</p>
          </button>
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-right">
            <TrendingUp className="w-6 h-6 text-green-600 mb-2" />
            <h4 className="font-medium text-gray-900">تقرير شهري</h4>
            <p className="text-sm text-gray-600 mt-1">عرض التقرير الشهري المفصل</p>
          </button>
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-right">
            <Download className="w-6 h-6 text-purple-600 mb-2" />
            <h4 className="font-medium text-gray-900">تصدير البيانات</h4>
            <p className="text-sm text-gray-600 mt-1">تصدير التقارير إلى Excel</p>
          </button>
        </div>
      </div>
    </div>
  )
}
