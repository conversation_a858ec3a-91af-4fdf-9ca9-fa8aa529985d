# دليل التثبيت والإعداد

## المتطلبات الأساسية

### 1. تثبيت Node.js
قم بتحميل وتثبيت Node.js من الموقع الرسمي:
- انتقل إلى: https://nodejs.org/
- حمل النسخة LTS (الموصى بها)
- قم بتثبيتها واتبع التعليمات
- أعد تشغيل الكمبيوتر بعد التثبيت

### 2. التحقق من التثبيت
افتح Command Prompt أو PowerShell وتأكد من التثبيت:
```bash
node --version
npm --version
```

## إعداد المشروع

### 1. تثبيت التبعيات
```bash
npm install
```

### 2. إعداد قاعدة البيانات (Supabase)

#### إنشاء مشروع Supabase جديد:
1. انتقل إلى: https://supabase.com/
2. أنشئ حساب جديد أو سجل دخول
3. اضغط "New Project"
4. اختر اسم المشروع وكلمة مرور قاعدة البيانات
5. انتظر حتى يتم إنشاء المشروع

#### الحصول على مفاتيح API:
1. في لوحة تحكم Supabase، انتقل إلى Settings > API
2. انسخ:
   - Project URL
   - anon public key

#### إعداد متغيرات البيئة:
1. انسخ ملف `.env.local.example` إلى `.env.local`
2. أضف مفاتيح Supabase:
```
NEXT_PUBLIC_SUPABASE_URL=your_project_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
```

### 3. إنشاء جداول قاعدة البيانات

في لوحة تحكم Supabase، انتقل إلى SQL Editor وقم بتشغيل الكود التالي:

```sql
-- إنشاء جدول العقارات
CREATE TABLE properties (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  address TEXT NOT NULL,
  property_type VARCHAR(100) NOT NULL,
  area DECIMAL(10,2),
  rooms INTEGER,
  bathrooms INTEGER,
  description TEXT,
  purchase_price DECIMAL(15,2),
  current_value DECIMAL(15,2),
  status VARCHAR(50) DEFAULT 'available',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المستأجرين
CREATE TABLE tenants (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  email VARCHAR(255),
  national_id VARCHAR(50),
  emergency_contact VARCHAR(255),
  emergency_phone VARCHAR(20),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول عقود الإيجار
CREATE TABLE rental_agreements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  monthly_rent DECIMAL(10,2) NOT NULL,
  security_deposit DECIMAL(10,2),
  status VARCHAR(50) DEFAULT 'active',
  contract_terms TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المدفوعات
CREATE TABLE payments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  rental_agreement_id UUID REFERENCES rental_agreements(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL,
  payment_date DATE,
  due_date DATE NOT NULL,
  payment_method VARCHAR(50),
  status VARCHAR(50) DEFAULT 'pending',
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المصروفات
CREATE TABLE expenses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  property_id UUID REFERENCES properties(id) ON DELETE SET NULL,
  category VARCHAR(100) NOT NULL,
  description TEXT NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  expense_date DATE NOT NULL,
  receipt_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- تفعيل Row Level Security
ALTER TABLE properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE rental_agreements ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE expenses ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات الأمان للعقارات
CREATE POLICY "Users can manage their own properties" ON properties
  FOR ALL USING (auth.uid() = user_id);

-- إنشاء سياسات الأمان للمستأجرين
CREATE POLICY "Users can manage their own tenants" ON tenants
  FOR ALL USING (auth.uid() = user_id);

-- إنشاء سياسات الأمان لعقود الإيجار
CREATE POLICY "Users can manage their own rentals" ON rental_agreements
  FOR ALL USING (auth.uid() = user_id);

-- إنشاء سياسات الأمان للمدفوعات
CREATE POLICY "Users can manage their own payments" ON payments
  FOR ALL USING (auth.uid() = user_id);

-- إنشاء سياسات الأمان للمصروفات
CREATE POLICY "Users can manage their own expenses" ON expenses
  FOR ALL USING (auth.uid() = user_id);
```

## تشغيل التطبيق

### 1. تشغيل خادم التطوير
```bash
npm run dev
```

### 2. فتح التطبيق
افتح المتصفح وانتقل إلى: http://localhost:3000

## استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ في الاتصال بـ Supabase
- تأكد من صحة متغيرات البيئة في `.env.local`
- تأكد من أن مشروع Supabase يعمل بشكل صحيح

#### 2. خطأ في تسجيل الدخول
- تأكد من تفعيل المصادقة في Supabase
- تحقق من إعدادات البريد الإلكتروني

#### 3. مشاكل في عرض البيانات
- تأكد من إنشاء جميع الجداول
- تحقق من سياسات RLS

### الحصول على المساعدة
- راجع وثائق Next.js: https://nextjs.org/docs
- راجع وثائق Supabase: https://supabase.com/docs
- راجع وثائق Tailwind CSS: https://tailwindcss.com/docs

## الخطوات التالية

بعد التثبيت الناجح:
1. أنشئ حساب جديد في التطبيق
2. أضف عقارك الأول
3. سجل مستأجر جديد
4. أنشئ عقد إيجار
5. ابدأ في تتبع المدفوعات والمصروفات

---

مبروك! أصبح نظام إدارة العقارات جاهزاً للاستخدام 🎉
