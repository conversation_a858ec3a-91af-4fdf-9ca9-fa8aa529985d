'use client'

import { useEffect, useState } from 'react'
import { Plus, Edit, Trash2, Users, Phone, Mail } from 'lucide-react'
import { supabase, Tenant } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import TenantForm from '@/components/Forms/TenantForm'

export default function TenantsPage() {
  const { user } = useAuth()
  const [tenants, setTenants] = useState<Tenant[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingTenant, setEditingTenant] = useState<Tenant | null>(null)

  useEffect(() => {
    if (user) {
      fetchTenants()
    }
  }, [user])

  const fetchTenants = async () => {
    try {
      const { data, error } = await supabase
        .from('tenants')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })

      if (error) throw error
      setTenants(data || [])
    } catch (error) {
      console.error('Error fetching tenants:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا المستأجر؟')) return

    try {
      const { error } = await supabase
        .from('tenants')
        .delete()
        .eq('id', id)

      if (error) throw error
      setTenants(tenants.filter(t => t.id !== id))
    } catch (error) {
      console.error('Error deleting tenant:', error)
      alert('حدث خطأ أثناء حذف المستأجر')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">إدارة المستأجرين</h1>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors flex items-center"
        >
          <Plus className="w-5 h-5 ml-2" />
          إضافة مستأجر جديد
        </button>
      </div>

      {tenants.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا يوجد مستأجرين</h3>
          <p className="text-gray-600 mb-4">ابدأ بإضافة مستأجرك الأول</p>
          <button
            onClick={() => setShowAddForm(true)}
            className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
          >
            إضافة مستأجر جديد
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tenants.map((tenant) => (
            <div key={tenant.id} className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">{tenant.name}</h3>
                <div className="flex space-x-2 space-x-reverse">
                  <button
                    onClick={() => setEditingTenant(tenant)}
                    className="text-primary-600 hover:text-primary-900 p-1"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(tenant.id)}
                    className="text-red-600 hover:text-red-900 p-1"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div className="space-y-3">
                {tenant.phone && (
                  <div className="flex items-center text-gray-600">
                    <Phone className="w-4 h-4 ml-2" />
                    <span className="text-sm">{tenant.phone}</span>
                  </div>
                )}
                
                {tenant.email && (
                  <div className="flex items-center text-gray-600">
                    <Mail className="w-4 h-4 ml-2" />
                    <span className="text-sm">{tenant.email}</span>
                  </div>
                )}
                
                {tenant.national_id && (
                  <div className="text-gray-600">
                    <span className="text-sm font-medium">رقم الهوية: </span>
                    <span className="text-sm">{tenant.national_id}</span>
                  </div>
                )}
                
                {tenant.emergency_contact && (
                  <div className="text-gray-600">
                    <span className="text-sm font-medium">جهة الاتصال للطوارئ: </span>
                    <span className="text-sm">{tenant.emergency_contact}</span>
                    {tenant.emergency_phone && (
                      <span className="text-sm block">{tenant.emergency_phone}</span>
                    )}
                  </div>
                )}
                
                {tenant.notes && (
                  <div className="text-gray-600">
                    <span className="text-sm font-medium">ملاحظات: </span>
                    <span className="text-sm">{tenant.notes}</span>
                  </div>
                )}
              </div>
              
              <div className="mt-4 pt-4 border-t border-gray-200">
                <span className="text-xs text-gray-500">
                  تم الإضافة: {new Date(tenant.created_at).toLocaleDateString('ar-SA')}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add/Edit Tenant Modal */}
      {(showAddForm || editingTenant) && (
        <TenantForm
          tenant={editingTenant}
          onSuccess={() => {
            setShowAddForm(false)
            setEditingTenant(null)
            fetchTenants()
          }}
          onCancel={() => {
            setShowAddForm(false)
            setEditingTenant(null)
          }}
        />
      )}
    </div>
  )
}
