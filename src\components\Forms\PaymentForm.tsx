'use client'

import { useState, useEffect } from 'react'
import { supabase, Payment, RentalAgreement } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'

interface PaymentFormProps {
  payment?: Payment | null
  onSuccess: () => void
  onCancel: () => void
}

export default function PaymentForm({ payment, onSuccess, onCancel }: PaymentFormProps) {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [rentals, setRentals] = useState<RentalAgreement[]>([])
  const [formData, setFormData] = useState({
    rental_agreement_id: payment?.rental_agreement_id || '',
    amount: payment?.amount?.toString() || '',
    payment_date: payment?.payment_date || '',
    due_date: payment?.due_date || '',
    payment_method: payment?.payment_method || '',
    status: payment?.status || 'pending',
    notes: payment?.notes || '',
  })

  useEffect(() => {
    if (user) {
      fetchRentals()
    }
  }, [user])

  const fetchRentals = async () => {
    try {
      const { data, error } = await supabase
        .from('rental_agreements')
        .select(`
          id,
          monthly_rent,
          property:properties(name, address),
          tenant:tenants(name)
        `)
        .eq('user_id', user?.id)
        .eq('status', 'active')

      if (error) throw error
      setRentals(data || [])
    } catch (error) {
      console.error('Error fetching rentals:', error)
    }
  }

  const handleRentalChange = (rentalId: string) => {
    const selectedRental = rentals.find(r => r.id === rentalId)
    if (selectedRental) {
      setFormData({
        ...formData,
        rental_agreement_id: rentalId,
        amount: selectedRental.monthly_rent.toString(),
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const paymentData = {
        user_id: user?.id,
        rental_agreement_id: formData.rental_agreement_id,
        amount: parseFloat(formData.amount),
        payment_date: formData.payment_date || null,
        due_date: formData.due_date,
        payment_method: formData.payment_method || null,
        status: formData.status,
        notes: formData.notes || null,
        updated_at: new Date().toISOString(),
      }

      if (payment) {
        // Update existing payment
        const { error } = await supabase
          .from('payments')
          .update(paymentData)
          .eq('id', payment.id)

        if (error) throw error
      } else {
        // Create new payment
        const { error } = await supabase
          .from('payments')
          .insert([paymentData])

        if (error) throw error
      }

      onSuccess()
    } catch (error) {
      console.error('Error saving payment:', error)
      alert('حدث خطأ أثناء حفظ الدفعة')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h3 className="text-lg font-medium text-gray-900 mb-6">
          {payment ? 'تعديل الدفعة' : 'إضافة دفعة جديدة'}
        </h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="rental_agreement_id" className="block text-sm font-medium text-gray-700 mb-1">
              عقد الإيجار *
            </label>
            <select
              id="rental_agreement_id"
              name="rental_agreement_id"
              required
              value={formData.rental_agreement_id}
              onChange={(e) => handleRentalChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">اختر عقد الإيجار</option>
              {rentals.map((rental) => (
                <option key={rental.id} value={rental.id}>
                  {rental.property?.name} - {rental.tenant?.name} ({rental.monthly_rent.toLocaleString()} ريال)
                </option>
              ))}
            </select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
                المبلغ (ريال) *
              </label>
              <input
                type="number"
                id="amount"
                name="amount"
                required
                min="0"
                step="0.01"
                value={formData.amount}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="مبلغ الدفعة"
              />
            </div>

            <div>
              <label htmlFor="due_date" className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ الاستحقاق *
              </label>
              <input
                type="date"
                id="due_date"
                name="due_date"
                required
                value={formData.due_date}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="payment_date" className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ الدفع
              </label>
              <input
                type="date"
                id="payment_date"
                name="payment_date"
                value={formData.payment_date}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <div>
              <label htmlFor="payment_method" className="block text-sm font-medium text-gray-700 mb-1">
                طريقة الدفع
              </label>
              <select
                id="payment_method"
                name="payment_method"
                value={formData.payment_method}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">اختر طريقة الدفع</option>
                <option value="نقدي">نقدي</option>
                <option value="تحويل بنكي">تحويل بنكي</option>
                <option value="شيك">شيك</option>
                <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                <option value="أخرى">أخرى</option>
              </select>
            </div>
          </div>

          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              حالة الدفعة
            </label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="pending">معلقة</option>
              <option value="paid">مدفوعة</option>
              <option value="cancelled">ملغية</option>
            </select>
          </div>

          <div>
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              ملاحظات
            </label>
            <textarea
              id="notes"
              name="notes"
              rows={3}
              value={formData.notes}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="أي ملاحظات إضافية (اختياري)"
            />
          </div>

          <div className="flex space-x-3 space-x-reverse pt-4">
            <button
              type="submit"
              disabled={loading}
              className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50"
            >
              {loading ? 'جاري الحفظ...' : payment ? 'تحديث الدفعة' : 'إضافة الدفعة'}
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="bg-gray-200 text-gray-800 px-6 py-2 rounded-lg hover:bg-gray-300 transition-colors"
            >
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
