'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import Link from 'next/link'

export default function Home() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && user) {
      router.push('/dashboard')
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <main className="min-h-screen p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8 text-gray-800">
          نظام إدارة العقارات والإيجارات
        </h1>
        <div className="bg-white rounded-lg shadow-lg p-8">
          <p className="text-lg text-gray-600 text-center mb-6">
            مرحباً بك في نظام إدارة العقارات والإيجارات الشامل
          </p>

          <div className="flex justify-center space-x-4 space-x-reverse mb-8">
            <Link
              href="/auth/login"
              className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
            >
              تسجيل الدخول
            </Link>
            <Link
              href="/auth/register"
              className="bg-gray-200 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-300 transition-colors"
            >
              إنشاء حساب جديد
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
              <h3 className="text-xl font-semibold text-blue-800 mb-3">إدارة العقارات</h3>
              <p className="text-blue-600">إضافة وتعديل وإدارة جميع العقارات</p>
            </div>
            <div className="bg-green-50 p-6 rounded-lg border border-green-200">
              <h3 className="text-xl font-semibold text-green-800 mb-3">إدارة المستأجرين</h3>
              <p className="text-green-600">متابعة بيانات المستأجرين وتاريخ الإيجار</p>
            </div>
            <div className="bg-purple-50 p-6 rounded-lg border border-purple-200">
              <h3 className="text-xl font-semibold text-purple-800 mb-3">التقارير المالية</h3>
              <p className="text-purple-600">تتبع الإيرادات والمصروفات والأرباح</p>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}
