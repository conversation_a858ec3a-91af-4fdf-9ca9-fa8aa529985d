<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العقارات والإيجارات - النسخة الكاملة</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        body { font-family: 'Cairo', system-ui, sans-serif; direction: rtl; }
        .sidebar { position: fixed; right: 0; top: 0; height: 100vh; width: 256px; background: white; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); z-index: 50; }
        .main-content { margin-right: 256px; min-height: 100vh; background: #f9fafb; }
        .nav-item { display: flex; align-items: center; padding: 12px 16px; margin: 4px 16px; border-radius: 8px; color: #6b7280; text-decoration: none; transition: all 0.2s; cursor: pointer; }
        .nav-item:hover { background: #f3f4f6; color: #374151; }
        .nav-item.active { background: #dbeafe; color: #1d4ed8; border-right: 4px solid #2563eb; }
        .nav-item i { margin-left: 12px; width: 20px; height: 20px; }
        .card { background: white; border-radius: 8px; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); padding: 24px; margin-bottom: 24px; }
        .btn-primary { background: #2563eb; color: white; padding: 8px 16px; border-radius: 8px; border: none; cursor: pointer; transition: background 0.2s; }
        .btn-primary:hover { background: #1d4ed8; }
        .btn-secondary { background: #6b7280; color: white; padding: 8px 16px; border-radius: 8px; border: none; cursor: pointer; margin-left: 8px; }
        .btn-danger { background: #dc2626; color: white; padding: 8px 16px; border-radius: 8px; border: none; cursor: pointer; margin-left: 8px; }
        .form-input { width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; margin-bottom: 16px; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 100; }
        .modal-content { background: white; margin: 5% auto; padding: 20px; border-radius: 8px; width: 90%; max-width: 600px; max-height: 80vh; overflow-y: auto; }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { padding: 12px; text-align: right; border-bottom: 1px solid #e5e7eb; }
        .table th { background: #f9fafb; font-weight: 600; }
        .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 600; }
        .status-available { background: #dcfce7; color: #166534; }
        .status-rented { background: #dbeafe; color: #1e40af; }
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-paid { background: #dcfce7; color: #166534; }
        .status-overdue { background: #fee2e2; color: #dc2626; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Login Page -->
    <div id="login-page" class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div class="max-w-md w-full space-y-8 p-8">
            <div class="text-center">
                <div class="mx-auto h-20 w-20 bg-blue-600 rounded-full flex items-center justify-center mb-6">
                    <i data-lucide="building-2" class="h-10 w-10 text-white"></i>
                </div>
                <h2 class="text-3xl font-bold text-gray-900 mb-2">نظام إدارة العقارات</h2>
                <p class="text-gray-600">نظام شامل لإدارة العقارات والإيجارات</p>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-8">
                <div class="mb-6">
                    <div class="flex border-b border-gray-200">
                        <button id="login-tab" class="flex-1 py-2 px-4 text-center font-medium text-blue-600 border-b-2 border-blue-600">
                            تسجيل الدخول
                        </button>
                        <button id="register-tab" class="flex-1 py-2 px-4 text-center font-medium text-gray-500 hover:text-gray-700">
                            إنشاء حساب
                        </button>
                    </div>
                </div>

                <!-- Login Form -->
                <div id="login-form" class="space-y-6">
                    <div>
                        <label for="login-email" class="block text-sm font-medium text-gray-700 mb-2">
                            البريد الإلكتروني
                        </label>
                        <input type="email" id="login-email" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="أدخل بريدك الإلكتروني">
                    </div>

                    <div>
                        <label for="login-password" class="block text-sm font-medium text-gray-700 mb-2">
                            كلمة المرور
                        </label>
                        <input type="password" id="login-password" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="أدخل كلمة المرور">
                    </div>

                    <div class="flex items-center justify-between">
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="mr-2 text-sm text-gray-600">تذكرني</span>
                        </label>
                        <a href="#" class="text-sm text-blue-600 hover:text-blue-500">نسيت كلمة المرور؟</a>
                    </div>

                    <button type="button" onclick="handleLogin()"
                        class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200">
                        تسجيل الدخول
                    </button>
                </div>

                <!-- Register Form -->
                <div id="register-form" class="space-y-6" style="display: none;">
                    <div>
                        <label for="register-name" class="block text-sm font-medium text-gray-700 mb-2">
                            الاسم الكامل
                        </label>
                        <input type="text" id="register-name" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="أدخل اسمك الكامل">
                    </div>

                    <div>
                        <label for="register-email" class="block text-sm font-medium text-gray-700 mb-2">
                            البريد الإلكتروني
                        </label>
                        <input type="email" id="register-email" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="أدخل بريدك الإلكتروني">
                    </div>

                    <div>
                        <label for="register-password" class="block text-sm font-medium text-gray-700 mb-2">
                            كلمة المرور
                        </label>
                        <input type="password" id="register-password" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="أدخل كلمة المرور">
                    </div>

                    <div>
                        <label for="register-confirm-password" class="block text-sm font-medium text-gray-700 mb-2">
                            تأكيد كلمة المرور
                        </label>
                        <input type="password" id="register-confirm-password" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="أعد إدخال كلمة المرور">
                    </div>

                    <div>
                        <label for="register-company" class="block text-sm font-medium text-gray-700 mb-2">
                            اسم الشركة (اختياري)
                        </label>
                        <input type="text" id="register-company"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="اسم شركتك أو مؤسستك">
                    </div>

                    <button type="button" onclick="handleRegister()"
                        class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition duration-200">
                        إنشاء حساب جديد
                    </button>
                </div>

                <!-- Demo Login -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <p class="text-center text-sm text-gray-600 mb-3">أو جرب النظام مباشرة</p>
                    <button type="button" onclick="demoLogin()"
                        class="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition duration-200">
                        دخول تجريبي
                    </button>
                </div>
            </div>

            <div class="text-center text-sm text-gray-600">
                <p>© 2024 نظام إدارة العقارات. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </div>

    <!-- Main App (Hidden initially) -->
    <div id="main-app" style="display: none;">
        <!-- User Info Bar -->
        <div class="bg-white border-b border-gray-200 px-6 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="h-8 w-8 bg-blue-600 rounded-full flex items-center justify-center ml-3">
                        <span class="text-white text-sm font-medium" id="user-avatar">م</span>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900" id="user-name">مرحباً</p>
                        <p class="text-xs text-gray-500" id="user-email"><EMAIL></p>
                    </div>
                </div>
                <button onclick="handleLogout()" class="text-sm text-gray-600 hover:text-gray-900 flex items-center">
                    <i data-lucide="log-out" class="w-4 h-4 ml-1"></i>
                    تسجيل الخروج
                </button>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="sidebar" style="top: 57px; height: calc(100vh - 57px);">
        <div class="flex items-center justify-center h-16 px-4 bg-blue-600">
            <h1 class="text-xl font-bold text-white">إدارة العقارات</h1>
        </div>
        
        <nav class="flex-1 px-4 py-6 space-y-2">
            <div class="nav-item active" onclick="showPage('dashboard')">
                <i data-lucide="home"></i>
                لوحة التحكم
            </div>
            <div class="nav-item" onclick="showPage('properties')">
                <i data-lucide="building"></i>
                العقارات
            </div>
            <div class="nav-item" onclick="showPage('tenants')">
                <i data-lucide="users"></i>
                المستأجرين
            </div>
            <div class="nav-item" onclick="showPage('rentals')">
                <i data-lucide="file-text"></i>
                عقود الإيجار
            </div>
            <div class="nav-item" onclick="showPage('payments')">
                <i data-lucide="credit-card"></i>
                المدفوعات
            </div>
            <div class="nav-item" onclick="showPage('expenses')">
                <i data-lucide="receipt"></i>
                المصروفات
            </div>
            <div class="nav-item" onclick="showPage('reports')">
                <i data-lucide="trending-up"></i>
                التقارير المالية
            </div>
        </nav>
    </div>

        <!-- Main Content -->
        <div class="main-content" style="padding-top: 57px;">
        <!-- Dashboard Page -->
        <div id="dashboard" class="page p-6">
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-3xl font-bold text-gray-900">لوحة التحكم</h1>
                <p class="text-gray-600">مرحباً بك في نظام إدارة العقارات الكامل</p>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="card bg-blue-50 border border-blue-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">إجمالي العقارات</p>
                            <p class="text-2xl font-bold text-blue-600 mt-1" id="total-properties">0</p>
                        </div>
                        <i data-lucide="building" class="w-8 h-8 text-blue-600"></i>
                    </div>
                </div>
                
                <div class="card bg-green-50 border border-green-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">إجمالي المستأجرين</p>
                            <p class="text-2xl font-bold text-green-600 mt-1" id="total-tenants">0</p>
                        </div>
                        <i data-lucide="users" class="w-8 h-8 text-green-600"></i>
                    </div>
                </div>
                
                <div class="card bg-purple-50 border border-purple-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">عقود الإيجار النشطة</p>
                            <p class="text-2xl font-bold text-purple-600 mt-1" id="active-rentals">0</p>
                        </div>
                        <i data-lucide="file-text" class="w-8 h-8 text-purple-600"></i>
                    </div>
                </div>
                
                <div class="card bg-yellow-50 border border-yellow-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">الدخل الشهري</p>
                            <p class="text-2xl font-bold text-yellow-600 mt-1" id="monthly-income">0 ريال</p>
                        </div>
                        <i data-lucide="trending-up" class="w-8 h-8 text-yellow-600"></i>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">إجراءات سريعة</h2>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <button class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-right" onclick="openModal('property-modal')">
                        <i data-lucide="building" class="w-8 h-8 text-blue-600 mb-2"></i>
                        <h3 class="font-medium text-gray-900">إضافة عقار</h3>
                        <p class="text-sm text-gray-600 mt-1">أضف عقار جديد</p>
                    </button>
                    <button class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-right" onclick="openModal('tenant-modal')">
                        <i data-lucide="users" class="w-8 h-8 text-green-600 mb-2"></i>
                        <h3 class="font-medium text-gray-900">إضافة مستأجر</h3>
                        <p class="text-sm text-gray-600 mt-1">سجل مستأجر جديد</p>
                    </button>
                    <button class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-right" onclick="openModal('rental-modal')">
                        <i data-lucide="file-text" class="w-8 h-8 text-purple-600 mb-2"></i>
                        <h3 class="font-medium text-gray-900">إنشاء عقد</h3>
                        <p class="text-sm text-gray-600 mt-1">أنشئ عقد إيجار</p>
                    </button>
                    <button class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-right" onclick="openModal('payment-modal')">
                        <i data-lucide="credit-card" class="w-8 h-8 text-orange-600 mb-2"></i>
                        <h3 class="font-medium text-gray-900">تسجيل دفعة</h3>
                        <p class="text-sm text-gray-600 mt-1">سجل دفعة جديدة</p>
                    </button>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">النشاط الأخير</h2>
                <div id="recent-activity" class="space-y-3">
                    <p class="text-gray-600">لا توجد أنشطة حديثة</p>
                </div>
            </div>
        </div>

        <!-- Properties Page -->
        <div id="properties" class="page p-6" style="display: none;">
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-3xl font-bold text-gray-900">إدارة العقارات</h1>
                <button class="btn-primary flex items-center" onclick="openModal('property-modal')">
                    <i data-lucide="plus" class="w-5 h-5 ml-2"></i>
                    إضافة عقار جديد
                </button>
            </div>

            <div class="card">
                <table class="table">
                    <thead>
                        <tr>
                            <th>اسم العقار</th>
                            <th>العنوان</th>
                            <th>النوع</th>
                            <th>الحالة</th>
                            <th>الإيجار الشهري</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="properties-table">
                        <tr>
                            <td colspan="6" class="text-center text-gray-500">لا توجد عقارات مسجلة</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Tenants Page -->
        <div id="tenants" class="page p-6" style="display: none;">
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-3xl font-bold text-gray-900">إدارة المستأجرين</h1>
                <button class="btn-primary flex items-center" onclick="openModal('tenant-modal')">
                    <i data-lucide="plus" class="w-5 h-5 ml-2"></i>
                    إضافة مستأجر جديد
                </button>
            </div>

            <div class="card">
                <table class="table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>رقم الهوية</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="tenants-table">
                        <tr>
                            <td colspan="5" class="text-center text-gray-500">لا يوجد مستأجرين مسجلين</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Rentals Page -->
        <div id="rentals" class="page p-6" style="display: none;">
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-3xl font-bold text-gray-900">عقود الإيجار</h1>
                <button class="btn-primary flex items-center" onclick="openModal('rental-modal')">
                    <i data-lucide="plus" class="w-5 h-5 ml-2"></i>
                    إنشاء عقد جديد
                </button>
            </div>

            <div class="card">
                <table class="table">
                    <thead>
                        <tr>
                            <th>العقار</th>
                            <th>المستأجر</th>
                            <th>تاريخ البداية</th>
                            <th>تاريخ النهاية</th>
                            <th>الإيجار الشهري</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="rentals-table">
                        <tr>
                            <td colspan="7" class="text-center text-gray-500">لا توجد عقود إيجار</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Payments Page -->
        <div id="payments" class="page p-6" style="display: none;">
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-3xl font-bold text-gray-900">إدارة المدفوعات</h1>
                <button class="btn-primary flex items-center" onclick="openModal('payment-modal')">
                    <i data-lucide="plus" class="w-5 h-5 ml-2"></i>
                    إضافة دفعة جديدة
                </button>
            </div>

            <div class="card">
                <table class="table">
                    <thead>
                        <tr>
                            <th>العقار</th>
                            <th>المستأجر</th>
                            <th>المبلغ</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>تاريخ الدفع</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="payments-table">
                        <tr>
                            <td colspan="7" class="text-center text-gray-500">لا توجد مدفوعات مسجلة</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Expenses Page -->
        <div id="expenses" class="page p-6" style="display: none;">
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-3xl font-bold text-gray-900">إدارة المصروفات</h1>
                <button class="btn-primary flex items-center" onclick="openModal('expense-modal')">
                    <i data-lucide="plus" class="w-5 h-5 ml-2"></i>
                    إضافة مصروف جديد
                </button>
            </div>

            <div class="card">
                <table class="table">
                    <thead>
                        <tr>
                            <th>الوصف</th>
                            <th>الفئة</th>
                            <th>المبلغ</th>
                            <th>التاريخ</th>
                            <th>العقار</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="expenses-table">
                        <tr>
                            <td colspan="6" class="text-center text-gray-500">لا توجد مصروفات مسجلة</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Reports Page -->
        <div id="reports" class="page p-6" style="display: none;">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">التقارير المالية</h1>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="card bg-green-50 border border-green-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">إجمالي الإيرادات</h3>
                    <p class="text-3xl font-bold text-green-600" id="total-income">0 ريال</p>
                </div>
                
                <div class="card bg-red-50 border border-red-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">إجمالي المصروفات</h3>
                    <p class="text-3xl font-bold text-red-600" id="total-expenses">0 ريال</p>
                </div>
                
                <div class="card bg-blue-50 border border-blue-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">صافي الربح</h3>
                    <p class="text-3xl font-bold text-blue-600" id="net-profit">0 ريال</p>
                </div>
            </div>

            <div class="card">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">تقرير مفصل</h3>
                <div id="detailed-report">
                    <p class="text-gray-600">سيتم عرض التقرير المفصل هنا بناءً على البيانات المدخلة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- Property Modal -->
    <div id="property-modal" class="modal">
        <div class="modal-content">
            <h2 class="text-xl font-bold mb-4">إضافة عقار جديد</h2>
            <form id="property-form">
                <input type="text" id="property-name" class="form-input" placeholder="اسم العقار" required>
                <input type="text" id="property-address" class="form-input" placeholder="العنوان" required>
                <select id="property-type" class="form-input" required>
                    <option value="">نوع العقار</option>
                    <option value="شقة">شقة</option>
                    <option value="فيلا">فيلا</option>
                    <option value="مكتب">مكتب</option>
                    <option value="محل">محل تجاري</option>
                </select>
                <input type="number" id="property-rent" class="form-input" placeholder="الإيجار الشهري (ريال)">
                <select id="property-status" class="form-input">
                    <option value="available">متاح</option>
                    <option value="rented">مؤجر</option>
                    <option value="maintenance">تحت الصيانة</option>
                </select>
                <div class="flex justify-end">
                    <button type="button" class="btn-secondary" onclick="closeModal('property-modal')">إلغاء</button>
                    <button type="submit" class="btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Tenant Modal -->
    <div id="tenant-modal" class="modal">
        <div class="modal-content">
            <h2 class="text-xl font-bold mb-4">إضافة مستأجر جديد</h2>
            <form id="tenant-form">
                <input type="text" id="tenant-name" class="form-input" placeholder="الاسم الكامل" required>
                <input type="tel" id="tenant-phone" class="form-input" placeholder="رقم الهاتف">
                <input type="email" id="tenant-email" class="form-input" placeholder="البريد الإلكتروني">
                <input type="text" id="tenant-id-number" class="form-input" placeholder="رقم الهوية">
                <div class="flex justify-end">
                    <button type="button" class="btn-secondary" onclick="closeModal('tenant-modal')">إلغاء</button>
                    <button type="submit" class="btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Rental Modal -->
    <div id="rental-modal" class="modal">
        <div class="modal-content">
            <h2 class="text-xl font-bold mb-4">إنشاء عقد إيجار جديد</h2>
            <form id="rental-form">
                <select id="rental-property" class="form-input" required>
                    <option value="">اختر العقار</option>
                </select>
                <select id="rental-tenant" class="form-input" required>
                    <option value="">اختر المستأجر</option>
                </select>
                <input type="date" id="rental-start" class="form-input" required>
                <input type="date" id="rental-end" class="form-input" required>
                <input type="number" id="rental-amount" class="form-input" placeholder="مبلغ الإيجار الشهري" required>
                <div class="flex justify-end">
                    <button type="button" class="btn-secondary" onclick="closeModal('rental-modal')">إلغاء</button>
                    <button type="submit" class="btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Payment Modal -->
    <div id="payment-modal" class="modal">
        <div class="modal-content">
            <h2 class="text-xl font-bold mb-4">تسجيل دفعة جديدة</h2>
            <form id="payment-form">
                <select id="payment-rental" class="form-input" required>
                    <option value="">اختر عقد الإيجار</option>
                </select>
                <input type="number" id="payment-amount" class="form-input" placeholder="مبلغ الدفعة" required>
                <input type="date" id="payment-due" class="form-input" required>
                <input type="date" id="payment-date" class="form-input">
                <select id="payment-status" class="form-input">
                    <option value="pending">معلقة</option>
                    <option value="paid">مدفوعة</option>
                </select>
                <div class="flex justify-end">
                    <button type="button" class="btn-secondary" onclick="closeModal('payment-modal')">إلغاء</button>
                    <button type="submit" class="btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Expense Modal -->
    <div id="expense-modal" class="modal">
        <div class="modal-content">
            <h2 class="text-xl font-bold mb-4">إضافة مصروف جديد</h2>
            <form id="expense-form">
                <input type="text" id="expense-description" class="form-input" placeholder="وصف المصروف" required>
                <select id="expense-category" class="form-input" required>
                    <option value="">فئة المصروف</option>
                    <option value="صيانة">صيانة</option>
                    <option value="كهرباء">كهرباء</option>
                    <option value="مياه">مياه</option>
                    <option value="تأمين">تأمين</option>
                    <option value="أخرى">أخرى</option>
                </select>
                <input type="number" id="expense-amount" class="form-input" placeholder="المبلغ" required>
                <input type="date" id="expense-date" class="form-input" required>
                <select id="expense-property" class="form-input">
                    <option value="">اختر العقار (اختياري)</option>
                </select>
                <div class="flex justify-end">
                    <button type="button" class="btn-secondary" onclick="closeModal('expense-modal')">إلغاء</button>
                    <button type="submit" class="btn-primary">حفظ</button>
                </div>
            </form>
        </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
