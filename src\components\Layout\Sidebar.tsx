'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  Home,
  Building,
  Users,
  FileText,
  CreditCard,
  Receipt,
  TrendingUp,
  Settings,
  LogOut
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'

const navigation = [
  { name: 'لوحة التحكم', href: '/dashboard', icon: Home },
  { name: 'العقارات', href: '/dashboard/properties', icon: Building },
  { name: 'المستأجرين', href: '/dashboard/tenants', icon: Users },
  { name: 'عقود الإيجار', href: '/dashboard/rentals', icon: FileText },
  { name: 'المدفوعات', href: '/dashboard/payments', icon: CreditCard },
  { name: 'المصروفات', href: '/dashboard/expenses', icon: Receipt },
  { name: 'التقارير المالية', href: '/dashboard/reports', icon: TrendingUp },
  { name: 'الإعدادات', href: '/dashboard/settings', icon: Settings },
]

export default function Sidebar() {
  const pathname = usePathname()
  const { signOut } = useAuth()

  return (
    <div className="flex flex-col w-64 bg-white shadow-lg">
      <div className="flex items-center justify-center h-16 px-4 bg-primary-600">
        <h1 className="text-xl font-bold text-white">إدارة العقارات</h1>
      </div>
      
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigation.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                isActive
                  ? 'bg-primary-100 text-primary-700 border-r-4 border-primary-600'
                  : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              <item.icon className="w-5 h-5 ml-3" />
              {item.name}
            </Link>
          )
        })}
      </nav>
      
      <div className="px-4 py-4 border-t border-gray-200">
        <button
          onClick={signOut}
          className="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-600 rounded-lg hover:bg-gray-100 hover:text-gray-900 transition-colors"
        >
          <LogOut className="w-5 h-5 ml-3" />
          تسجيل الخروج
        </button>
      </div>
    </div>
  )
}
