'use client'

import { useState, useEffect } from 'react'
import { supabase, Expense, Property } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'

interface ExpenseFormProps {
  expense?: Expense | null
  onSuccess: () => void
  onCancel: () => void
}

export default function ExpenseForm({ expense, onSuccess, onCancel }: ExpenseFormProps) {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [properties, setProperties] = useState<Property[]>([])
  const [formData, setFormData] = useState({
    property_id: expense?.property_id || '',
    category: expense?.category || '',
    description: expense?.description || '',
    amount: expense?.amount?.toString() || '',
    expense_date: expense?.expense_date || new Date().toISOString().split('T')[0],
    receipt_url: expense?.receipt_url || '',
  })

  const categories = [
    'صيانة',
    'كهرباء',
    'مياه',
    'تأمين',
    'ضرائب',
    'تسويق',
    'إدارة',
    'أخرى'
  ]

  useEffect(() => {
    if (user) {
      fetchProperties()
    }
  }, [user])

  const fetchProperties = async () => {
    try {
      const { data, error } = await supabase
        .from('properties')
        .select('id, name, address')
        .eq('user_id', user?.id)

      if (error) throw error
      setProperties(data || [])
    } catch (error) {
      console.error('Error fetching properties:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const expenseData = {
        user_id: user?.id,
        property_id: formData.property_id || null,
        category: formData.category,
        description: formData.description,
        amount: parseFloat(formData.amount),
        expense_date: formData.expense_date,
        receipt_url: formData.receipt_url || null,
        updated_at: new Date().toISOString(),
      }

      if (expense) {
        // Update existing expense
        const { error } = await supabase
          .from('expenses')
          .update(expenseData)
          .eq('id', expense.id)

        if (error) throw error
      } else {
        // Create new expense
        const { error } = await supabase
          .from('expenses')
          .insert([expenseData])

        if (error) throw error
      }

      onSuccess()
    } catch (error) {
      console.error('Error saving expense:', error)
      alert('حدث خطأ أثناء حفظ المصروف')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h3 className="text-lg font-medium text-gray-900 mb-6">
          {expense ? 'تعديل المصروف' : 'إضافة مصروف جديد'}
        </h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                فئة المصروف *
              </label>
              <select
                id="category"
                name="category"
                required
                value={formData.category}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">اختر فئة المصروف</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
                المبلغ (ريال) *
              </label>
              <input
                type="number"
                id="amount"
                name="amount"
                required
                min="0"
                step="0.01"
                value={formData.amount}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="مبلغ المصروف"
              />
            </div>
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              وصف المصروف *
            </label>
            <textarea
              id="description"
              name="description"
              required
              rows={3}
              value={formData.description}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="أدخل وصف تفصيلي للمصروف"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="expense_date" className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ المصروف *
              </label>
              <input
                type="date"
                id="expense_date"
                name="expense_date"
                required
                value={formData.expense_date}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <div>
              <label htmlFor="property_id" className="block text-sm font-medium text-gray-700 mb-1">
                العقار المرتبط (اختياري)
              </label>
              <select
                id="property_id"
                name="property_id"
                value={formData.property_id}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">لا يوجد عقار محدد</option>
                {properties.map((property) => (
                  <option key={property.id} value={property.id}>
                    {property.name} - {property.address}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <label htmlFor="receipt_url" className="block text-sm font-medium text-gray-700 mb-1">
              رابط الإيصال (اختياري)
            </label>
            <input
              type="url"
              id="receipt_url"
              name="receipt_url"
              value={formData.receipt_url}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="https://example.com/receipt.pdf"
            />
            <p className="text-xs text-gray-500 mt-1">
              يمكنك إضافة رابط لصورة أو ملف PDF للإيصال
            </p>
          </div>

          <div className="flex space-x-3 space-x-reverse pt-4">
            <button
              type="submit"
              disabled={loading}
              className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50"
            >
              {loading ? 'جاري الحفظ...' : expense ? 'تحديث المصروف' : 'إضافة المصروف'}
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="bg-gray-200 text-gray-800 px-6 py-2 rounded-lg hover:bg-gray-300 transition-colors"
            >
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
