// نظام إدارة العقارات والإيجارات - JavaScript الكامل

// قاعدة البيانات المحلية
let database = {
    users: [],
    properties: [],
    tenants: [],
    rentals: [],
    payments: [],
    expenses: []
};

// المستخدم الحالي
let currentUser = null;

// تحميل البيانات من localStorage
function loadData() {
    const savedData = localStorage.getItem('realEstateData');
    if (savedData) {
        database = JSON.parse(savedData);
    }
}

// حفظ البيانات في localStorage
function saveData() {
    localStorage.setItem('realEstateData', JSON.stringify(database));
}

// إنشاء ID فريد
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// ===== وظائف المصادقة =====

// التبديل بين تسجيل الدخول وإنشاء الحساب
document.addEventListener('DOMContentLoaded', function() {
    const loginTab = document.getElementById('login-tab');
    const registerTab = document.getElementById('register-tab');
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');

    loginTab.addEventListener('click', function() {
        loginTab.classList.add('text-blue-600', 'border-b-2', 'border-blue-600');
        loginTab.classList.remove('text-gray-500');
        registerTab.classList.add('text-gray-500');
        registerTab.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600');

        loginForm.style.display = 'block';
        registerForm.style.display = 'none';
    });

    registerTab.addEventListener('click', function() {
        registerTab.classList.add('text-blue-600', 'border-b-2', 'border-blue-600');
        registerTab.classList.remove('text-gray-500');
        loginTab.classList.add('text-gray-500');
        loginTab.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600');

        registerForm.style.display = 'block';
        loginForm.style.display = 'none';
    });

    // التحقق من وجود جلسة مفتوحة
    checkExistingSession();
});

// التحقق من وجود جلسة مفتوحة
function checkExistingSession() {
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        showMainApp();
    }
}

// تسجيل الدخول
function handleLogin() {
    const email = document.getElementById('login-email').value;
    const password = document.getElementById('login-password').value;

    if (!email || !password) {
        showMessage('يرجى إدخال البريد الإلكتروني وكلمة المرور', 'error');
        return;
    }

    // البحث عن المستخدم
    const user = database.users.find(u => u.email === email && u.password === password);

    if (user) {
        currentUser = user;
        localStorage.setItem('currentUser', JSON.stringify(user));
        showMessage('تم تسجيل الدخول بنجاح', 'success');
        setTimeout(() => {
            showMainApp();
        }, 1000);
    } else {
        showMessage('البريد الإلكتروني أو كلمة المرور غير صحيحة', 'error');
    }
}

// إنشاء حساب جديد
function handleRegister() {
    const name = document.getElementById('register-name').value;
    const email = document.getElementById('register-email').value;
    const password = document.getElementById('register-password').value;
    const confirmPassword = document.getElementById('register-confirm-password').value;
    const company = document.getElementById('register-company').value;

    // التحقق من البيانات
    if (!name || !email || !password) {
        showMessage('يرجى إدخال جميع البيانات المطلوبة', 'error');
        return;
    }

    if (password !== confirmPassword) {
        showMessage('كلمات المرور غير متطابقة', 'error');
        return;
    }

    if (password.length < 6) {
        showMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
        return;
    }

    // التحقق من عدم وجود المستخدم مسبقاً
    if (database.users.find(u => u.email === email)) {
        showMessage('هذا البريد الإلكتروني مسجل مسبقاً', 'error');
        return;
    }

    // إنشاء المستخدم الجديد
    const newUser = {
        id: generateId(),
        name: name,
        email: email,
        password: password,
        company: company,
        createdAt: new Date().toISOString()
    };

    database.users.push(newUser);
    saveData();

    showMessage('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول', 'success');

    // التبديل إلى نموذج تسجيل الدخول
    setTimeout(() => {
        document.getElementById('login-tab').click();
        document.getElementById('login-email').value = email;
    }, 1500);
}

// دخول تجريبي
function demoLogin() {
    // إنشاء مستخدم تجريبي إذا لم يكن موجوداً
    let demoUser = database.users.find(u => u.email === '<EMAIL>');

    if (!demoUser) {
        demoUser = {
            id: 'demo-user',
            name: 'مستخدم تجريبي',
            email: '<EMAIL>',
            password: 'demo123',
            company: 'شركة تجريبية',
            createdAt: new Date().toISOString()
        };
        database.users.push(demoUser);
        saveData();
    }

    currentUser = demoUser;
    localStorage.setItem('currentUser', JSON.stringify(demoUser));
    showMessage('تم الدخول كمستخدم تجريبي', 'success');

    setTimeout(() => {
        showMainApp();
        // إضافة بيانات تجريبية
        addDemoData();
    }, 1000);
}

// تسجيل الخروج
function handleLogout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        currentUser = null;
        localStorage.removeItem('currentUser');
        showLoginPage();
    }
}

// عرض الصفحة الرئيسية
function showMainApp() {
    document.getElementById('login-page').style.display = 'none';
    document.getElementById('main-app').style.display = 'block';

    // تحديث معلومات المستخدم
    document.getElementById('user-name').textContent = `مرحباً، ${currentUser.name}`;
    document.getElementById('user-email').textContent = currentUser.email;
    document.getElementById('user-avatar').textContent = currentUser.name.charAt(0);

    // تحميل البيانات وتحديث لوحة التحكم
    loadData();
    updateDashboard();

    // إعادة إنشاء الأيقونات
    lucide.createIcons();
}

// عرض صفحة تسجيل الدخول
function showLoginPage() {
    document.getElementById('main-app').style.display = 'none';
    document.getElementById('login-page').style.display = 'block';

    // مسح النماذج
    document.getElementById('login-email').value = '';
    document.getElementById('login-password').value = '';
    document.querySelectorAll('#register-form input').forEach(input => input.value = '');
}

// عرض الرسائل
function showMessage(message, type = 'info') {
    // إنشاء عنصر الرسالة
    const messageDiv = document.createElement('div');
    messageDiv.className = `fixed top-4 left-1/2 transform -translate-x-1/2 px-6 py-3 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    messageDiv.textContent = message;

    document.body.appendChild(messageDiv);

    // إزالة الرسالة بعد 3 ثوان
    setTimeout(() => {
        document.body.removeChild(messageDiv);
    }, 3000);
}

// إضافة بيانات تجريبية
function addDemoData() {
    // إضافة عقارات تجريبية
    if (database.properties.length === 0) {
        const demoProperties = [
            {
                id: generateId(),
                name: 'شقة الياسمين',
                address: 'حي الياسمين، الرياض',
                type: 'شقة',
                rent: 3500,
                status: 'rented',
                createdAt: new Date().toISOString()
            },
            {
                id: generateId(),
                name: 'فيلا النرجس',
                address: 'حي النرجس، الرياض',
                type: 'فيلا',
                rent: 8000,
                status: 'available',
                createdAt: new Date().toISOString()
            }
        ];
        database.properties.push(...demoProperties);
    }

    // إضافة مستأجرين تجريبيين
    if (database.tenants.length === 0) {
        const demoTenants = [
            {
                id: generateId(),
                name: 'أحمد محمد السعيد',
                phone: '0501234567',
                email: '<EMAIL>',
                idNumber: '1234567890',
                createdAt: new Date().toISOString()
            }
        ];
        database.tenants.push(...demoTenants);
    }

    saveData();
    updateDashboard();
}

// عرض الصفحات
function showPage(pageId) {
    // إخفاء جميع الصفحات
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => page.style.display = 'none');
    
    // عرض الصفحة المحددة
    document.getElementById(pageId).style.display = 'block';
    
    // تحديث التنقل
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => item.classList.remove('active'));
    
    event.target.classList.add('active');
    
    // تحديث محتوى الصفحة
    switch(pageId) {
        case 'properties':
            displayProperties();
            break;
        case 'tenants':
            displayTenants();
            break;
        case 'rentals':
            displayRentals();
            updateRentalSelects();
            break;
        case 'payments':
            displayPayments();
            updatePaymentSelects();
            break;
        case 'expenses':
            displayExpenses();
            updateExpenseSelects();
            break;
        case 'reports':
            displayReports();
            break;
    }
}

// فتح وإغلاق النوافذ المنبثقة
function openModal(modalId) {
    document.getElementById(modalId).style.display = 'block';
    
    // تحديث القوائم المنسدلة
    if (modalId === 'rental-modal') updateRentalSelects();
    if (modalId === 'payment-modal') updatePaymentSelects();
    if (modalId === 'expense-modal') updateExpenseSelects();
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
    // إعادة تعيين النموذج
    const form = document.querySelector(`#${modalId} form`);
    if (form) form.reset();
}

// إدارة العقارات
document.getElementById('property-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const property = {
        id: generateId(),
        name: document.getElementById('property-name').value,
        address: document.getElementById('property-address').value,
        type: document.getElementById('property-type').value,
        rent: parseFloat(document.getElementById('property-rent').value) || 0,
        status: document.getElementById('property-status').value,
        createdAt: new Date().toISOString()
    };
    
    database.properties.push(property);
    saveData();
    displayProperties();
    updateDashboard();
    closeModal('property-modal');
    
    addActivity(`تم إضافة عقار جديد: ${property.name}`);
});

function displayProperties() {
    const tbody = document.getElementById('properties-table');
    
    if (database.properties.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-gray-500">لا توجد عقارات مسجلة</td></tr>';
        return;
    }
    
    tbody.innerHTML = database.properties.map(property => `
        <tr>
            <td class="font-medium">${property.name}</td>
            <td>${property.address}</td>
            <td>${property.type}</td>
            <td><span class="status-badge status-${property.status}">${getStatusText(property.status)}</span></td>
            <td class="font-semibold text-green-600">${property.rent.toLocaleString()} ريال</td>
            <td>
                <button class="btn-danger text-sm" onclick="deleteProperty('${property.id}')">حذف</button>
            </td>
        </tr>
    `).join('');
}

function deleteProperty(id) {
    if (confirm('هل أنت متأكد من حذف هذا العقار؟')) {
        database.properties = database.properties.filter(p => p.id !== id);
        saveData();
        displayProperties();
        updateDashboard();
        addActivity('تم حذف عقار');
    }
}

// إدارة المستأجرين
document.getElementById('tenant-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const tenant = {
        id: generateId(),
        name: document.getElementById('tenant-name').value,
        phone: document.getElementById('tenant-phone').value,
        email: document.getElementById('tenant-email').value,
        idNumber: document.getElementById('tenant-id-number').value,
        createdAt: new Date().toISOString()
    };
    
    database.tenants.push(tenant);
    saveData();
    displayTenants();
    updateDashboard();
    closeModal('tenant-modal');
    
    addActivity(`تم إضافة مستأجر جديد: ${tenant.name}`);
});

function displayTenants() {
    const tbody = document.getElementById('tenants-table');
    
    if (database.tenants.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-gray-500">لا يوجد مستأجرين مسجلين</td></tr>';
        return;
    }
    
    tbody.innerHTML = database.tenants.map(tenant => `
        <tr>
            <td class="font-medium">${tenant.name}</td>
            <td>${tenant.phone || '-'}</td>
            <td>${tenant.email || '-'}</td>
            <td>${tenant.idNumber || '-'}</td>
            <td>
                <button class="btn-danger text-sm" onclick="deleteTenant('${tenant.id}')">حذف</button>
            </td>
        </tr>
    `).join('');
}

function deleteTenant(id) {
    if (confirm('هل أنت متأكد من حذف هذا المستأجر؟')) {
        database.tenants = database.tenants.filter(t => t.id !== id);
        saveData();
        displayTenants();
        updateDashboard();
        addActivity('تم حذف مستأجر');
    }
}

// إدارة عقود الإيجار
function updateRentalSelects() {
    const propertySelect = document.getElementById('rental-property');
    const tenantSelect = document.getElementById('rental-tenant');
    
    // تحديث قائمة العقارات المتاحة
    propertySelect.innerHTML = '<option value="">اختر العقار</option>' +
        database.properties.filter(p => p.status === 'available').map(property => 
            `<option value="${property.id}">${property.name} - ${property.address}</option>`
        ).join('');
    
    // تحديث قائمة المستأجرين
    tenantSelect.innerHTML = '<option value="">اختر المستأجر</option>' +
        database.tenants.map(tenant => 
            `<option value="${tenant.id}">${tenant.name}</option>`
        ).join('');
}

document.getElementById('rental-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const rental = {
        id: generateId(),
        propertyId: document.getElementById('rental-property').value,
        tenantId: document.getElementById('rental-tenant').value,
        startDate: document.getElementById('rental-start').value,
        endDate: document.getElementById('rental-end').value,
        monthlyRent: parseFloat(document.getElementById('rental-amount').value),
        status: 'active',
        createdAt: new Date().toISOString()
    };
    
    database.rentals.push(rental);
    
    // تحديث حالة العقار إلى مؤجر
    const property = database.properties.find(p => p.id === rental.propertyId);
    if (property) {
        property.status = 'rented';
    }
    
    saveData();
    displayRentals();
    updateDashboard();
    closeModal('rental-modal');
    
    const propertyName = property ? property.name : 'عقار';
    const tenant = database.tenants.find(t => t.id === rental.tenantId);
    const tenantName = tenant ? tenant.name : 'مستأجر';
    addActivity(`تم إنشاء عقد إيجار جديد: ${propertyName} - ${tenantName}`);
});

function displayRentals() {
    const tbody = document.getElementById('rentals-table');
    
    if (database.rentals.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-gray-500">لا توجد عقود إيجار</td></tr>';
        return;
    }
    
    tbody.innerHTML = database.rentals.map(rental => {
        const property = database.properties.find(p => p.id === rental.propertyId);
        const tenant = database.tenants.find(t => t.id === rental.tenantId);
        
        return `
            <tr>
                <td class="font-medium">${property ? property.name : 'عقار محذوف'}</td>
                <td>${tenant ? tenant.name : 'مستأجر محذوف'}</td>
                <td>${new Date(rental.startDate).toLocaleDateString('ar-SA')}</td>
                <td>${new Date(rental.endDate).toLocaleDateString('ar-SA')}</td>
                <td class="font-semibold text-green-600">${rental.monthlyRent.toLocaleString()} ريال</td>
                <td><span class="status-badge status-${rental.status}">${getStatusText(rental.status)}</span></td>
                <td>
                    <button class="btn-danger text-sm" onclick="deleteRental('${rental.id}')">حذف</button>
                </td>
            </tr>
        `;
    }).join('');
}

function deleteRental(id) {
    if (confirm('هل أنت متأكد من حذف عقد الإيجار هذا؟')) {
        const rental = database.rentals.find(r => r.id === id);
        if (rental) {
            // إعادة العقار إلى حالة متاح
            const property = database.properties.find(p => p.id === rental.propertyId);
            if (property) {
                property.status = 'available';
            }
        }
        
        database.rentals = database.rentals.filter(r => r.id !== id);
        saveData();
        displayRentals();
        updateDashboard();
        addActivity('تم حذف عقد إيجار');
    }
}

// إدارة المدفوعات
function updatePaymentSelects() {
    const rentalSelect = document.getElementById('payment-rental');
    
    rentalSelect.innerHTML = '<option value="">اختر عقد الإيجار</option>' +
        database.rentals.filter(r => r.status === 'active').map(rental => {
            const property = database.properties.find(p => p.id === rental.propertyId);
            const tenant = database.tenants.find(t => t.id === rental.tenantId);
            const propertyName = property ? property.name : 'عقار';
            const tenantName = tenant ? tenant.name : 'مستأجر';
            return `<option value="${rental.id}">${propertyName} - ${tenantName}</option>`;
        }).join('');
}

document.getElementById('payment-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const payment = {
        id: generateId(),
        rentalId: document.getElementById('payment-rental').value,
        amount: parseFloat(document.getElementById('payment-amount').value),
        dueDate: document.getElementById('payment-due').value,
        paymentDate: document.getElementById('payment-date').value || null,
        status: document.getElementById('payment-status').value,
        createdAt: new Date().toISOString()
    };
    
    database.payments.push(payment);
    saveData();
    displayPayments();
    updateDashboard();
    closeModal('payment-modal');
    
    addActivity(`تم تسجيل دفعة جديدة: ${payment.amount.toLocaleString()} ريال`);
});

function displayPayments() {
    const tbody = document.getElementById('payments-table');
    
    if (database.payments.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-gray-500">لا توجد مدفوعات مسجلة</td></tr>';
        return;
    }
    
    tbody.innerHTML = database.payments.map(payment => {
        const rental = database.rentals.find(r => r.id === payment.rentalId);
        const property = rental ? database.properties.find(p => p.id === rental.propertyId) : null;
        const tenant = rental ? database.tenants.find(t => t.id === rental.tenantId) : null;
        
        // تحديد حالة الدفعة
        let statusClass = payment.status;
        if (payment.status === 'pending' && new Date(payment.dueDate) < new Date()) {
            statusClass = 'overdue';
        }
        
        return `
            <tr>
                <td class="font-medium">${property ? property.name : 'عقار محذوف'}</td>
                <td>${tenant ? tenant.name : 'مستأجر محذوف'}</td>
                <td class="font-semibold text-green-600">${payment.amount.toLocaleString()} ريال</td>
                <td>${new Date(payment.dueDate).toLocaleDateString('ar-SA')}</td>
                <td>${payment.paymentDate ? new Date(payment.paymentDate).toLocaleDateString('ar-SA') : '-'}</td>
                <td><span class="status-badge status-${statusClass}">${getPaymentStatusText(payment.status, payment.dueDate)}</span></td>
                <td>
                    <button class="btn-danger text-sm" onclick="deletePayment('${payment.id}')">حذف</button>
                </td>
            </tr>
        `;
    }).join('');
}

function deletePayment(id) {
    if (confirm('هل أنت متأكد من حذف هذه الدفعة؟')) {
        database.payments = database.payments.filter(p => p.id !== id);
        saveData();
        displayPayments();
        updateDashboard();
        addActivity('تم حذف دفعة');
    }
}

// إدارة المصروفات
function updateExpenseSelects() {
    const propertySelect = document.getElementById('expense-property');
    
    propertySelect.innerHTML = '<option value="">اختر العقار (اختياري)</option>' +
        database.properties.map(property => 
            `<option value="${property.id}">${property.name} - ${property.address}</option>`
        ).join('');
}

document.getElementById('expense-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const expense = {
        id: generateId(),
        description: document.getElementById('expense-description').value,
        category: document.getElementById('expense-category').value,
        amount: parseFloat(document.getElementById('expense-amount').value),
        date: document.getElementById('expense-date').value,
        propertyId: document.getElementById('expense-property').value || null,
        createdAt: new Date().toISOString()
    };
    
    database.expenses.push(expense);
    saveData();
    displayExpenses();
    updateDashboard();
    closeModal('expense-modal');
    
    addActivity(`تم إضافة مصروف جديد: ${expense.description} - ${expense.amount.toLocaleString()} ريال`);
});

function displayExpenses() {
    const tbody = document.getElementById('expenses-table');
    
    if (database.expenses.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-gray-500">لا توجد مصروفات مسجلة</td></tr>';
        return;
    }
    
    tbody.innerHTML = database.expenses.map(expense => {
        const property = expense.propertyId ? database.properties.find(p => p.id === expense.propertyId) : null;
        
        return `
            <tr>
                <td class="font-medium">${expense.description}</td>
                <td>${expense.category}</td>
                <td class="font-semibold text-red-600">${expense.amount.toLocaleString()} ريال</td>
                <td>${new Date(expense.date).toLocaleDateString('ar-SA')}</td>
                <td>${property ? property.name : '-'}</td>
                <td>
                    <button class="btn-danger text-sm" onclick="deleteExpense('${expense.id}')">حذف</button>
                </td>
            </tr>
        `;
    }).join('');
}

function deleteExpense(id) {
    if (confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
        database.expenses = database.expenses.filter(e => e.id !== id);
        saveData();
        displayExpenses();
        updateDashboard();
        addActivity('تم حذف مصروف');
    }
}

// التقارير المالية
function displayReports() {
    const totalIncome = database.payments.filter(p => p.status === 'paid').reduce((sum, p) => sum + p.amount, 0);
    const totalExpenses = database.expenses.reduce((sum, e) => sum + e.amount, 0);
    const netProfit = totalIncome - totalExpenses;
    
    document.getElementById('total-income').textContent = totalIncome.toLocaleString() + ' ريال';
    document.getElementById('total-expenses').textContent = totalExpenses.toLocaleString() + ' ريال';
    document.getElementById('net-profit').textContent = netProfit.toLocaleString() + ' ريال';
    
    // تقرير مفصل
    const detailedReport = document.getElementById('detailed-report');
    detailedReport.innerHTML = `
        <div class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <h4 class="font-semibold text-gray-900 mb-2">الإيرادات حسب الشهر</h4>
                    <p class="text-gray-600">سيتم إضافة رسم بياني هنا</p>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900 mb-2">المصروفات حسب الفئة</h4>
                    <p class="text-gray-600">سيتم إضافة رسم بياني هنا</p>
                </div>
            </div>
            <div>
                <h4 class="font-semibold text-gray-900 mb-2">ملخص الأداء</h4>
                <ul class="text-gray-600 space-y-1">
                    <li>• إجمالي العقارات: ${database.properties.length}</li>
                    <li>• العقارات المؤجرة: ${database.properties.filter(p => p.status === 'rented').length}</li>
                    <li>• العقارات المتاحة: ${database.properties.filter(p => p.status === 'available').length}</li>
                    <li>• إجمالي المستأجرين: ${database.tenants.length}</li>
                    <li>• عقود الإيجار النشطة: ${database.rentals.filter(r => r.status === 'active').length}</li>
                    <li>• المدفوعات المعلقة: ${database.payments.filter(p => p.status === 'pending').length}</li>
                </ul>
            </div>
        </div>
    `;
}

// تحديث لوحة التحكم
function updateDashboard() {
    document.getElementById('total-properties').textContent = database.properties.length;
    document.getElementById('total-tenants').textContent = database.tenants.length;
    document.getElementById('active-rentals').textContent = database.rentals.filter(r => r.status === 'active').length;
    
    const monthlyIncome = database.rentals.filter(r => r.status === 'active').reduce((sum, r) => sum + r.monthlyRent, 0);
    document.getElementById('monthly-income').textContent = monthlyIncome.toLocaleString() + ' ريال';
}

// إضافة نشاط جديد
function addActivity(activity) {
    const activityDiv = document.getElementById('recent-activity');
    const activityItem = document.createElement('div');
    activityItem.className = 'flex items-center justify-between py-3 border-b border-gray-100';
    activityItem.innerHTML = `
        <div class="flex items-center">
            <div class="w-2 h-2 bg-green-500 rounded-full ml-3"></div>
            <span class="text-gray-900">${activity}</span>
        </div>
        <span class="text-sm text-gray-500">الآن</span>
    `;
    
    if (activityDiv.children.length === 1 && activityDiv.children[0].textContent.includes('لا توجد أنشطة')) {
        activityDiv.innerHTML = '';
    }
    
    activityDiv.insertBefore(activityItem, activityDiv.firstChild);
    
    // الاحتفاظ بآخر 5 أنشطة فقط
    while (activityDiv.children.length > 5) {
        activityDiv.removeChild(activityDiv.lastChild);
    }
}

// دوال مساعدة
function getStatusText(status) {
    const statusMap = {
        'available': 'متاح',
        'rented': 'مؤجر',
        'maintenance': 'تحت الصيانة',
        'active': 'نشط',
        'expired': 'منتهي',
        'terminated': 'مُنهى'
    };
    return statusMap[status] || status;
}

function getPaymentStatusText(status, dueDate) {
    if (status === 'paid') return 'مدفوع';
    if (status === 'pending' && new Date(dueDate) < new Date()) return 'متأخر';
    if (status === 'pending') return 'معلق';
    return 'ملغي';
}

// إغلاق النوافذ المنبثقة عند النقر خارجها
window.onclick = function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    lucide.createIcons();
    loadData();

    // إضافة مستمعي الأحداث للنماذج
    setupFormListeners();
});

// إعداد مستمعي الأحداث للنماذج
function setupFormListeners() {
    // نماذج تسجيل الدخول
    document.getElementById('login-email').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') handleLogin();
    });

    document.getElementById('login-password').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') handleLogin();
    });
}
