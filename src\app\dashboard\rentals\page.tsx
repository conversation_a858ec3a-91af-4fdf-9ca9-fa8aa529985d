'use client'

import { useEffect, useState } from 'react'
import { Plus, Edit, Trash2, FileText, Calendar, DollarSign } from 'lucide-react'
import { supabase, RentalAgreement } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import RentalForm from '@/components/Forms/RentalForm'

export default function RentalsPage() {
  const { user } = useAuth()
  const [rentals, setRentals] = useState<RentalAgreement[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingRental, setEditingRental] = useState<RentalAgreement | null>(null)

  useEffect(() => {
    if (user) {
      fetchRentals()
    }
  }, [user])

  const fetchRentals = async () => {
    try {
      const { data, error } = await supabase
        .from('rental_agreements')
        .select(`
          *,
          property:properties(name, address),
          tenant:tenants(name, phone)
        `)
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })

      if (error) throw error
      setRentals(data || [])
    } catch (error) {
      console.error('Error fetching rentals:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف عقد الإيجار هذا؟')) return

    try {
      const { error } = await supabase
        .from('rental_agreements')
        .delete()
        .eq('id', id)

      if (error) throw error
      setRentals(rentals.filter(r => r.id !== id))
    } catch (error) {
      console.error('Error deleting rental:', error)
      alert('حدث خطأ أثناء حذف عقد الإيجار')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'expired':
        return 'bg-red-100 text-red-800'
      case 'terminated':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-yellow-100 text-yellow-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'نشط'
      case 'expired':
        return 'منتهي'
      case 'terminated':
        return 'مُنهى'
      default:
        return 'معلق'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">عقود الإيجار</h1>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors flex items-center"
        >
          <Plus className="w-5 h-5 ml-2" />
          إنشاء عقد جديد
        </button>
      </div>

      {rentals.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد عقود إيجار</h3>
          <p className="text-gray-600 mb-4">ابدأ بإنشاء عقد الإيجار الأول</p>
          <button
            onClick={() => setShowAddForm(true)}
            className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
          >
            إنشاء عقد جديد
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {rentals.map((rental) => (
            <div key={rental.id} className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <FileText className="w-6 h-6 text-primary-600 ml-2" />
                  <h3 className="text-lg font-semibold text-gray-900">
                    {rental.property?.name || 'عقار غير محدد'}
                  </h3>
                </div>
                <div className="flex space-x-2 space-x-reverse">
                  <button
                    onClick={() => setEditingRental(rental)}
                    className="text-primary-600 hover:text-primary-900 p-1"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(rental.id)}
                    className="text-red-600 hover:text-red-900 p-1"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">المستأجر:</span>
                  <span className="text-sm text-gray-900">{rental.tenant?.name || 'غير محدد'}</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">العنوان:</span>
                  <span className="text-sm text-gray-900">{rental.property?.address || 'غير محدد'}</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">الإيجار الشهري:</span>
                  <div className="flex items-center">
                    <DollarSign className="w-4 h-4 text-green-600 ml-1" />
                    <span className="text-sm font-semibold text-green-600">
                      {rental.monthly_rent.toLocaleString()} ريال
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">فترة العقد:</span>
                  <div className="flex items-center text-sm text-gray-900">
                    <Calendar className="w-4 h-4 ml-1" />
                    {new Date(rental.start_date).toLocaleDateString('ar-SA')} - {new Date(rental.end_date).toLocaleDateString('ar-SA')}
                  </div>
                </div>

                {rental.security_deposit && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-600">التأمين:</span>
                    <span className="text-sm text-gray-900">
                      {rental.security_deposit.toLocaleString()} ريال
                    </span>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">الحالة:</span>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(rental.status)}`}>
                    {getStatusText(rental.status)}
                  </span>
                </div>

                {rental.contract_terms && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <span className="text-sm font-medium text-gray-600">شروط العقد:</span>
                    <p className="text-sm text-gray-900 mt-1">{rental.contract_terms}</p>
                  </div>
                )}
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200">
                <span className="text-xs text-gray-500">
                  تم الإنشاء: {new Date(rental.created_at).toLocaleDateString('ar-SA')}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add/Edit Rental Modal */}
      {(showAddForm || editingRental) && (
        <RentalForm
          rental={editingRental}
          onSuccess={() => {
            setShowAddForm(false)
            setEditingRental(null)
            fetchRentals()
          }}
          onCancel={() => {
            setShowAddForm(false)
            setEditingRental(null)
          }}
        />
      )}
    </div>
  )
}
