// نظام إدارة العقارات والإيجارات مع Supabase

// متغيرات عامة
let database = {
    properties: [],
    tenants: [],
    rentals: [],
    payments: [],
    expenses: []
};

// ===== وظائف المصادقة =====

// التبديل بين تسجيل الدخول وإنشاء الحساب
document.addEventListener('DOMContentLoaded', function() {
    const loginTab = document.getElementById('login-tab');
    const registerTab = document.getElementById('register-tab');
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');

    loginTab.addEventListener('click', function() {
        loginTab.classList.add('text-blue-600', 'border-b-2', 'border-blue-600');
        loginTab.classList.remove('text-gray-500');
        registerTab.classList.add('text-gray-500');
        registerTab.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600');
        
        loginForm.style.display = 'block';
        registerForm.style.display = 'none';
    });

    registerTab.addEventListener('click', function() {
        registerTab.classList.add('text-blue-600', 'border-b-2', 'border-blue-600');
        registerTab.classList.remove('text-gray-500');
        loginTab.classList.add('text-gray-500');
        loginTab.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600');
        
        registerForm.style.display = 'block';
        loginForm.style.display = 'none';
    });

    // التحقق من وجود جلسة مفتوحة
    checkExistingSession();
    
    // إعداد مستمعي الأحداث
    setupFormListeners();
    
    // تهيئة الأيقونات
    lucide.createIcons();
});

// التحقق من وجود جلسة مفتوحة
async function checkExistingSession() {
    const { session, user } = await getCurrentSession();
    if (session && user) {
        currentUser = user;
        currentSession = session;
        showMainApp();
    }
}

// تسجيل الدخول
async function handleLogin() {
    const email = document.getElementById('login-email').value;
    const password = document.getElementById('login-password').value;

    if (!email || !password) {
        showMessage('يرجى إدخال البريد الإلكتروني وكلمة المرور', 'error');
        return;
    }

    showMessage('جاري تسجيل الدخول...', 'info');

    const { data, error } = await signInWithSupabase(email, password);
    
    if (error) {
        showMessage('خطأ في البريد الإلكتروني أو كلمة المرور', 'error');
        console.error('Login error:', error);
    } else {
        showMessage('تم تسجيل الدخول بنجاح', 'success');
        setTimeout(() => {
            showMainApp();
        }, 1000);
    }
}

// إنشاء حساب جديد
async function handleRegister() {
    const name = document.getElementById('register-name').value;
    const email = document.getElementById('register-email').value;
    const password = document.getElementById('register-password').value;
    const confirmPassword = document.getElementById('register-confirm-password').value;
    const company = document.getElementById('register-company').value;

    // التحقق من البيانات
    if (!name || !email || !password) {
        showMessage('يرجى إدخال جميع البيانات المطلوبة', 'error');
        return;
    }

    if (password !== confirmPassword) {
        showMessage('كلمات المرور غير متطابقة', 'error');
        return;
    }

    if (password.length < 6) {
        showMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
        return;
    }

    showMessage('جاري إنشاء الحساب...', 'info');

    const { data, error } = await signUpWithSupabase(email, password, name, company);
    
    if (error) {
        if (error.message.includes('already registered')) {
            showMessage('هذا البريد الإلكتروني مسجل مسبقاً', 'error');
        } else {
            showMessage('حدث خطأ أثناء إنشاء الحساب', 'error');
        }
        console.error('Registration error:', error);
    } else {
        showMessage('تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني', 'success');
        
        // التبديل إلى نموذج تسجيل الدخول
        setTimeout(() => {
            document.getElementById('login-tab').click();
            document.getElementById('login-email').value = email;
        }, 2000);
    }
}

// دخول تجريبي
async function demoLogin() {
    showMessage('جاري إنشاء حساب تجريبي...', 'info');
    
    // إنشاء حساب تجريبي
    const demoEmail = `demo_${Date.now()}@example.com`;
    const demoPassword = 'demo123456';
    const demoName = 'مستخدم تجريبي';
    const demoCompany = 'شركة تجريبية';

    const { data, error } = await signUpWithSupabase(demoEmail, demoPassword, demoName, demoCompany);
    
    if (error) {
        showMessage('حدث خطأ في إنشاء الحساب التجريبي', 'error');
        console.error('Demo signup error:', error);
        return;
    }

    // تسجيل الدخول بالحساب التجريبي
    const { data: loginData, error: loginError } = await signInWithSupabase(demoEmail, demoPassword);
    
    if (loginError) {
        showMessage('حدث خطأ في تسجيل الدخول التجريبي', 'error');
        console.error('Demo login error:', loginError);
        return;
    }

    showMessage('تم الدخول كمستخدم تجريبي', 'success');
    
    setTimeout(async () => {
        showMainApp();
        // إضافة بيانات تجريبية
        await addDemoData();
    }, 1000);
}

// تسجيل الخروج
async function handleLogout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        const { error } = await signOutFromSupabase();
        if (error) {
            showMessage('حدث خطأ أثناء تسجيل الخروج', 'error');
        } else {
            showLoginPage();
        }
    }
}

// عرض الصفحة الرئيسية
function showMainApp() {
    document.getElementById('login-page').style.display = 'none';
    document.getElementById('main-app').style.display = 'block';
    
    // تحديث معلومات المستخدم
    if (currentUser) {
        document.getElementById('user-name').textContent = `مرحباً، ${currentUser.name}`;
        document.getElementById('user-email').textContent = currentUser.email;
        document.getElementById('user-avatar').textContent = currentUser.name.charAt(0);
    }
    
    // تحميل البيانات وتحديث لوحة التحكم
    loadAllData();
    
    // إعادة إنشاء الأيقونات
    lucide.createIcons();
}

// عرض صفحة تسجيل الدخول
function showLoginPage() {
    document.getElementById('main-app').style.display = 'none';
    document.getElementById('login-page').style.display = 'block';
    
    // مسح النماذج
    document.getElementById('login-email').value = '';
    document.getElementById('login-password').value = '';
    document.querySelectorAll('#register-form input').forEach(input => input.value = '');
}

// عرض الرسائل
function showMessage(message, type = 'info') {
    // إزالة الرسائل السابقة
    const existingMessages = document.querySelectorAll('.message-toast');
    existingMessages.forEach(msg => msg.remove());
    
    // إنشاء عنصر الرسالة
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-toast fixed top-4 left-1/2 transform -translate-x-1/2 px-6 py-3 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    // إزالة الرسالة بعد 3 ثوان
    setTimeout(() => {
        if (document.body.contains(messageDiv)) {
            document.body.removeChild(messageDiv);
        }
    }, 3000);
}

// تحميل جميع البيانات
async function loadAllData() {
    try {
        // تحميل العقارات
        const { data: properties } = await getProperties();
        database.properties = properties || [];

        // تحميل المستأجرين
        const { data: tenants } = await getTenants();
        database.tenants = tenants || [];

        // تحميل عقود الإيجار
        const { data: rentals } = await getRentals();
        database.rentals = rentals || [];

        // تحميل المدفوعات
        const { data: payments } = await getPayments();
        database.payments = payments || [];

        // تحميل المصروفات
        const { data: expenses } = await getExpenses();
        database.expenses = expenses || [];

        // تحديث لوحة التحكم
        updateDashboard();
        
    } catch (error) {
        console.error('Error loading data:', error);
        showMessage('حدث خطأ في تحميل البيانات', 'error');
    }
}

// إضافة بيانات تجريبية
async function addDemoData() {
    try {
        // إضافة عقارات تجريبية
        if (database.properties.length === 0) {
            const property1 = await addProperty({
                name: 'شقة الياسمين',
                address: 'حي الياسمين، الرياض',
                property_type: 'شقة',
                monthly_rent: 3500,
                status: 'available'
            });

            const property2 = await addProperty({
                name: 'فيلا النرجس',
                address: 'حي النرجس، الرياض',
                property_type: 'فيلا',
                monthly_rent: 8000,
                status: 'available'
            });

            // إضافة مستأجر تجريبي
            const tenant1 = await addTenant({
                name: 'أحمد محمد السعيد',
                phone: '0501234567',
                email: '<EMAIL>',
                national_id: '1234567890'
            });

            // تحديث البيانات
            await loadAllData();
            
            showMessage('تم إضافة بيانات تجريبية', 'success');
        }
    } catch (error) {
        console.error('Error adding demo data:', error);
    }
}

// ===== وظائف عرض الصفحات =====

// عرض الصفحات
function showPage(pageId) {
    // إخفاء جميع الصفحات
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => page.style.display = 'none');
    
    // عرض الصفحة المحددة
    document.getElementById(pageId).style.display = 'block';
    
    // تحديث التنقل
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => item.classList.remove('active'));
    
    event.target.classList.add('active');
    
    // تحديث محتوى الصفحة
    switch(pageId) {
        case 'properties':
            displayProperties();
            break;
        case 'tenants':
            displayTenants();
            break;
        case 'rentals':
            displayRentals();
            updateRentalSelects();
            break;
        case 'payments':
            displayPayments();
            updatePaymentSelects();
            break;
        case 'expenses':
            displayExpenses();
            updateExpenseSelects();
            break;
        case 'reports':
            displayReports();
            break;
    }
}

// فتح وإغلاق النوافذ المنبثقة
function openModal(modalId) {
    document.getElementById(modalId).style.display = 'block';
    
    // تحديث القوائم المنسدلة
    if (modalId === 'rental-modal') updateRentalSelects();
    if (modalId === 'payment-modal') updatePaymentSelects();
    if (modalId === 'expense-modal') updateExpenseSelects();
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
    // إعادة تعيين النموذج
    const form = document.querySelector(`#${modalId} form`);
    if (form) form.reset();
}

// تحديث لوحة التحكم
function updateDashboard() {
    document.getElementById('total-properties').textContent = database.properties.length;
    document.getElementById('total-tenants').textContent = database.tenants.length;
    document.getElementById('active-rentals').textContent = database.rentals.filter(r => r.status === 'active').length;
    
    const monthlyIncome = database.rentals.filter(r => r.status === 'active').reduce((sum, r) => sum + (r.monthly_rent || 0), 0);
    document.getElementById('monthly-income').textContent = monthlyIncome.toLocaleString() + ' ريال';
}

// إعداد مستمعي الأحداث للنماذج
function setupFormListeners() {
    // نماذج تسجيل الدخول
    document.getElementById('login-email').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') handleLogin();
    });
    
    document.getElementById('login-password').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') handleLogin();
    });

    // نماذج البيانات
    setupDataFormListeners();
}

// إعداد نماذج البيانات
function setupDataFormListeners() {
    // نموذج العقارات
    document.getElementById('property-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const propertyData = {
            name: document.getElementById('property-name').value,
            address: document.getElementById('property-address').value,
            property_type: document.getElementById('property-type').value,
            monthly_rent: parseFloat(document.getElementById('property-rent').value) || 0,
            status: document.getElementById('property-status').value
        };
        
        const { data, error } = await addProperty(propertyData);
        
        if (error) {
            showMessage('حدث خطأ في إضافة العقار', 'error');
            console.error('Error adding property:', error);
        } else {
            showMessage('تم إضافة العقار بنجاح', 'success');
            closeModal('property-modal');
            await loadAllData();
            displayProperties();
        }
    });

    // نموذج المستأجرين
    document.getElementById('tenant-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const tenantData = {
            name: document.getElementById('tenant-name').value,
            phone: document.getElementById('tenant-phone').value,
            email: document.getElementById('tenant-email').value,
            national_id: document.getElementById('tenant-id-number').value
        };
        
        const { data, error } = await addTenant(tenantData);
        
        if (error) {
            showMessage('حدث خطأ في إضافة المستأجر', 'error');
            console.error('Error adding tenant:', error);
        } else {
            showMessage('تم إضافة المستأجر بنجاح', 'success');
            closeModal('tenant-modal');
            await loadAllData();
            displayTenants();
        }
    });

    // نموذج عقود الإيجار
    document.getElementById('rental-form').addEventListener('submit', async function(e) {
        e.preventDefault();

        const rentalData = {
            property_id: document.getElementById('rental-property').value,
            tenant_id: document.getElementById('rental-tenant').value,
            start_date: document.getElementById('rental-start').value,
            end_date: document.getElementById('rental-end').value,
            monthly_rent: parseFloat(document.getElementById('rental-amount').value),
            status: 'active'
        };

        const { data, error } = await addRental(rentalData);

        if (error) {
            showMessage('حدث خطأ في إنشاء عقد الإيجار', 'error');
            console.error('Error adding rental:', error);
        } else {
            // تحديث حالة العقار إلى مؤجر
            await updateProperty(rentalData.property_id, { status: 'rented' });

            showMessage('تم إنشاء عقد الإيجار بنجاح', 'success');
            closeModal('rental-modal');
            await loadAllData();
            displayRentals();
        }
    });

    // نموذج المدفوعات
    document.getElementById('payment-form').addEventListener('submit', async function(e) {
        e.preventDefault();

        const paymentData = {
            rental_agreement_id: document.getElementById('payment-rental').value,
            amount: parseFloat(document.getElementById('payment-amount').value),
            due_date: document.getElementById('payment-due').value,
            payment_date: document.getElementById('payment-date').value || null,
            status: document.getElementById('payment-status').value
        };

        const { data, error } = await addPayment(paymentData);

        if (error) {
            showMessage('حدث خطأ في إضافة الدفعة', 'error');
            console.error('Error adding payment:', error);
        } else {
            showMessage('تم إضافة الدفعة بنجاح', 'success');
            closeModal('payment-modal');
            await loadAllData();
            displayPayments();
        }
    });

    // نموذج المصروفات
    document.getElementById('expense-form').addEventListener('submit', async function(e) {
        e.preventDefault();

        const expenseData = {
            description: document.getElementById('expense-description').value,
            category: document.getElementById('expense-category').value,
            amount: parseFloat(document.getElementById('expense-amount').value),
            expense_date: document.getElementById('expense-date').value,
            property_id: document.getElementById('expense-property').value || null
        };

        const { data, error } = await addExpense(expenseData);

        if (error) {
            showMessage('حدث خطأ في إضافة المصروف', 'error');
            console.error('Error adding expense:', error);
        } else {
            showMessage('تم إضافة المصروف بنجاح', 'success');
            closeModal('expense-modal');
            await loadAllData();
            displayExpenses();
        }
    });
}

// ===== وظائف عرض البيانات =====

// عرض العقارات
function displayProperties() {
    const tbody = document.getElementById('properties-table');

    if (database.properties.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-gray-500">لا توجد عقارات مسجلة</td></tr>';
        return;
    }

    tbody.innerHTML = database.properties.map(property => `
        <tr>
            <td class="font-medium">${property.name}</td>
            <td>${property.address}</td>
            <td>${property.property_type}</td>
            <td><span class="status-badge status-${property.status}">${getStatusText(property.status)}</span></td>
            <td class="font-semibold text-green-600">${(property.monthly_rent || 0).toLocaleString()} ريال</td>
            <td>
                <button class="btn-danger text-sm" onclick="deletePropertyHandler('${property.id}')">حذف</button>
            </td>
        </tr>
    `).join('');
}

// حذف عقار
async function deletePropertyHandler(id) {
    if (confirm('هل أنت متأكد من حذف هذا العقار؟')) {
        const { error } = await deleteProperty(id);
        if (error) {
            showMessage('حدث خطأ في حذف العقار', 'error');
            console.error('Error deleting property:', error);
        } else {
            showMessage('تم حذف العقار بنجاح', 'success');
            await loadAllData();
            displayProperties();
        }
    }
}

// عرض المستأجرين
function displayTenants() {
    const tbody = document.getElementById('tenants-table');

    if (database.tenants.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-gray-500">لا يوجد مستأجرين مسجلين</td></tr>';
        return;
    }

    tbody.innerHTML = database.tenants.map(tenant => `
        <tr>
            <td class="font-medium">${tenant.name}</td>
            <td>${tenant.phone || '-'}</td>
            <td>${tenant.email || '-'}</td>
            <td>${tenant.national_id || '-'}</td>
            <td>
                <button class="btn-danger text-sm" onclick="deleteTenantHandler('${tenant.id}')">حذف</button>
            </td>
        </tr>
    `).join('');
}

// حذف مستأجر
async function deleteTenantHandler(id) {
    if (confirm('هل أنت متأكد من حذف هذا المستأجر؟')) {
        const { error } = await deleteTenant(id);
        if (error) {
            showMessage('حدث خطأ في حذف المستأجر', 'error');
            console.error('Error deleting tenant:', error);
        } else {
            showMessage('تم حذف المستأجر بنجاح', 'success');
            await loadAllData();
            displayTenants();
        }
    }
}

// عرض عقود الإيجار
function displayRentals() {
    const tbody = document.getElementById('rentals-table');

    if (database.rentals.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-gray-500">لا توجد عقود إيجار</td></tr>';
        return;
    }

    tbody.innerHTML = database.rentals.map(rental => `
        <tr>
            <td class="font-medium">${rental.property?.name || 'عقار محذوف'}</td>
            <td>${rental.tenant?.name || 'مستأجر محذوف'}</td>
            <td>${new Date(rental.start_date).toLocaleDateString('ar-SA')}</td>
            <td>${new Date(rental.end_date).toLocaleDateString('ar-SA')}</td>
            <td class="font-semibold text-green-600">${rental.monthly_rent.toLocaleString()} ريال</td>
            <td><span class="status-badge status-${rental.status}">${getStatusText(rental.status)}</span></td>
            <td>
                <button class="btn-danger text-sm" onclick="deleteRentalHandler('${rental.id}')">حذف</button>
            </td>
        </tr>
    `).join('');
}

// حذف عقد إيجار
async function deleteRentalHandler(id) {
    if (confirm('هل أنت متأكد من حذف عقد الإيجار هذا؟')) {
        const rental = database.rentals.find(r => r.id === id);

        const { error } = await deleteRental(id);
        if (error) {
            showMessage('حدث خطأ في حذف عقد الإيجار', 'error');
            console.error('Error deleting rental:', error);
        } else {
            // إعادة العقار إلى حالة متاح
            if (rental && rental.property_id) {
                await updateProperty(rental.property_id, { status: 'available' });
            }

            showMessage('تم حذف عقد الإيجار بنجاح', 'success');
            await loadAllData();
            displayRentals();
        }
    }
}

// عرض المدفوعات
function displayPayments() {
    const tbody = document.getElementById('payments-table');

    if (database.payments.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-gray-500">لا توجد مدفوعات مسجلة</td></tr>';
        return;
    }

    tbody.innerHTML = database.payments.map(payment => {
        // تحديد حالة الدفعة
        let statusClass = payment.status;
        if (payment.status === 'pending' && new Date(payment.due_date) < new Date()) {
            statusClass = 'overdue';
        }

        return `
            <tr>
                <td class="font-medium">${payment.rental_agreement?.property?.name || 'عقار محذوف'}</td>
                <td>${payment.rental_agreement?.tenant?.name || 'مستأجر محذوف'}</td>
                <td class="font-semibold text-green-600">${payment.amount.toLocaleString()} ريال</td>
                <td>${new Date(payment.due_date).toLocaleDateString('ar-SA')}</td>
                <td>${payment.payment_date ? new Date(payment.payment_date).toLocaleDateString('ar-SA') : '-'}</td>
                <td><span class="status-badge status-${statusClass}">${getPaymentStatusText(payment.status, payment.due_date)}</span></td>
                <td>
                    <button class="btn-danger text-sm" onclick="deletePaymentHandler('${payment.id}')">حذف</button>
                </td>
            </tr>
        `;
    }).join('');
}

// حذف دفعة
async function deletePaymentHandler(id) {
    if (confirm('هل أنت متأكد من حذف هذه الدفعة؟')) {
        const { error } = await deletePayment(id);
        if (error) {
            showMessage('حدث خطأ في حذف الدفعة', 'error');
            console.error('Error deleting payment:', error);
        } else {
            showMessage('تم حذف الدفعة بنجاح', 'success');
            await loadAllData();
            displayPayments();
        }
    }
}

// عرض المصروفات
function displayExpenses() {
    const tbody = document.getElementById('expenses-table');

    if (database.expenses.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-gray-500">لا توجد مصروفات مسجلة</td></tr>';
        return;
    }

    tbody.innerHTML = database.expenses.map(expense => `
        <tr>
            <td class="font-medium">${expense.description}</td>
            <td>${expense.category}</td>
            <td class="font-semibold text-red-600">${expense.amount.toLocaleString()} ريال</td>
            <td>${new Date(expense.expense_date).toLocaleDateString('ar-SA')}</td>
            <td>${expense.property?.name || '-'}</td>
            <td>
                <button class="btn-danger text-sm" onclick="deleteExpenseHandler('${expense.id}')">حذف</button>
            </td>
        </tr>
    `).join('');
}

// حذف مصروف
async function deleteExpenseHandler(id) {
    if (confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
        const { error } = await deleteExpense(id);
        if (error) {
            showMessage('حدث خطأ في حذف المصروف', 'error');
            console.error('Error deleting expense:', error);
        } else {
            showMessage('تم حذف المصروف بنجاح', 'success');
            await loadAllData();
            displayExpenses();
        }
    }
}

// عرض التقارير
function displayReports() {
    const totalIncome = database.payments.filter(p => p.status === 'paid').reduce((sum, p) => sum + p.amount, 0);
    const totalExpenses = database.expenses.reduce((sum, e) => sum + e.amount, 0);
    const netProfit = totalIncome - totalExpenses;

    document.getElementById('total-income').textContent = totalIncome.toLocaleString() + ' ريال';
    document.getElementById('total-expenses').textContent = totalExpenses.toLocaleString() + ' ريال';
    document.getElementById('net-profit').textContent = netProfit.toLocaleString() + ' ريال';
}

// تحديث القوائم المنسدلة
function updateRentalSelects() {
    const propertySelect = document.getElementById('rental-property');
    const tenantSelect = document.getElementById('rental-tenant');

    // تحديث قائمة العقارات المتاحة
    propertySelect.innerHTML = '<option value="">اختر العقار</option>' +
        database.properties.filter(p => p.status === 'available').map(property =>
            `<option value="${property.id}">${property.name} - ${property.address}</option>`
        ).join('');

    // تحديث قائمة المستأجرين
    tenantSelect.innerHTML = '<option value="">اختر المستأجر</option>' +
        database.tenants.map(tenant =>
            `<option value="${tenant.id}">${tenant.name}</option>`
        ).join('');
}

function updatePaymentSelects() {
    const rentalSelect = document.getElementById('payment-rental');

    rentalSelect.innerHTML = '<option value="">اختر عقد الإيجار</option>' +
        database.rentals.filter(r => r.status === 'active').map(rental => {
            const propertyName = rental.property?.name || 'عقار';
            const tenantName = rental.tenant?.name || 'مستأجر';
            return `<option value="${rental.id}">${propertyName} - ${tenantName}</option>`;
        }).join('');
}

function updateExpenseSelects() {
    const propertySelect = document.getElementById('expense-property');

    propertySelect.innerHTML = '<option value="">اختر العقار (اختياري)</option>' +
        database.properties.map(property =>
            `<option value="${property.id}">${property.name} - ${property.address}</option>`
        ).join('');
}

// دوال مساعدة
function getStatusText(status) {
    const statusMap = {
        'available': 'متاح',
        'rented': 'مؤجر',
        'maintenance': 'تحت الصيانة',
        'active': 'نشط',
        'expired': 'منتهي',
        'terminated': 'مُنهى'
    };
    return statusMap[status] || status;
}

function getPaymentStatusText(status, dueDate) {
    if (status === 'paid') return 'مدفوع';
    if (status === 'pending' && new Date(dueDate) < new Date()) return 'متأخر';
    if (status === 'pending') return 'معلق';
    return 'ملغي';
}

// إغلاق النوافذ المنبثقة عند النقر خارجها
window.onclick = function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
}
