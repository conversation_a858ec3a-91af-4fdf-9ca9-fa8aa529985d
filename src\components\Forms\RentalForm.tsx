'use client'

import { useState, useEffect } from 'react'
import { supabase, RentalAgreement, Property, Tenant } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'

interface RentalFormProps {
  rental?: RentalAgreement | null
  onSuccess: () => void
  onCancel: () => void
}

export default function RentalForm({ rental, onSuccess, onCancel }: RentalFormProps) {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [properties, setProperties] = useState<Property[]>([])
  const [tenants, setTenants] = useState<Tenant[]>([])
  const [formData, setFormData] = useState({
    property_id: rental?.property_id || '',
    tenant_id: rental?.tenant_id || '',
    start_date: rental?.start_date || '',
    end_date: rental?.end_date || '',
    monthly_rent: rental?.monthly_rent?.toString() || '',
    security_deposit: rental?.security_deposit?.toString() || '',
    status: rental?.status || 'active',
    contract_terms: rental?.contract_terms || '',
  })

  useEffect(() => {
    if (user) {
      fetchProperties()
      fetchTenants()
    }
  }, [user])

  const fetchProperties = async () => {
    try {
      const { data, error } = await supabase
        .from('properties')
        .select('id, name, address')
        .eq('user_id', user?.id)
        .eq('status', 'available')

      if (error) throw error
      setProperties(data || [])
    } catch (error) {
      console.error('Error fetching properties:', error)
    }
  }

  const fetchTenants = async () => {
    try {
      const { data, error } = await supabase
        .from('tenants')
        .select('id, name, phone')
        .eq('user_id', user?.id)

      if (error) throw error
      setTenants(data || [])
    } catch (error) {
      console.error('Error fetching tenants:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const rentalData = {
        user_id: user?.id,
        property_id: formData.property_id,
        tenant_id: formData.tenant_id,
        start_date: formData.start_date,
        end_date: formData.end_date,
        monthly_rent: parseFloat(formData.monthly_rent),
        security_deposit: formData.security_deposit ? parseFloat(formData.security_deposit) : null,
        status: formData.status,
        contract_terms: formData.contract_terms || null,
        updated_at: new Date().toISOString(),
      }

      if (rental) {
        // Update existing rental
        const { error } = await supabase
          .from('rental_agreements')
          .update(rentalData)
          .eq('id', rental.id)

        if (error) throw error
      } else {
        // Create new rental
        const { error } = await supabase
          .from('rental_agreements')
          .insert([rentalData])

        if (error) throw error

        // Update property status to rented
        await supabase
          .from('properties')
          .update({ status: 'rented' })
          .eq('id', formData.property_id)
      }

      onSuccess()
    } catch (error) {
      console.error('Error saving rental:', error)
      alert('حدث خطأ أثناء حفظ عقد الإيجار')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h3 className="text-lg font-medium text-gray-900 mb-6">
          {rental ? 'تعديل عقد الإيجار' : 'إنشاء عقد إيجار جديد'}
        </h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="property_id" className="block text-sm font-medium text-gray-700 mb-1">
                العقار *
              </label>
              <select
                id="property_id"
                name="property_id"
                required
                value={formData.property_id}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">اختر العقار</option>
                {properties.map((property) => (
                  <option key={property.id} value={property.id}>
                    {property.name} - {property.address}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="tenant_id" className="block text-sm font-medium text-gray-700 mb-1">
                المستأجر *
              </label>
              <select
                id="tenant_id"
                name="tenant_id"
                required
                value={formData.tenant_id}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">اختر المستأجر</option>
                {tenants.map((tenant) => (
                  <option key={tenant.id} value={tenant.id}>
                    {tenant.name} {tenant.phone && `- ${tenant.phone}`}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="start_date" className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ بداية العقد *
              </label>
              <input
                type="date"
                id="start_date"
                name="start_date"
                required
                value={formData.start_date}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <div>
              <label htmlFor="end_date" className="block text-sm font-medium text-gray-700 mb-1">
                تاريخ انتهاء العقد *
              </label>
              <input
                type="date"
                id="end_date"
                name="end_date"
                required
                value={formData.end_date}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="monthly_rent" className="block text-sm font-medium text-gray-700 mb-1">
                الإيجار الشهري (ريال) *
              </label>
              <input
                type="number"
                id="monthly_rent"
                name="monthly_rent"
                required
                min="0"
                step="0.01"
                value={formData.monthly_rent}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="مبلغ الإيجار الشهري"
              />
            </div>

            <div>
              <label htmlFor="security_deposit" className="block text-sm font-medium text-gray-700 mb-1">
                مبلغ التأمين (ريال)
              </label>
              <input
                type="number"
                id="security_deposit"
                name="security_deposit"
                min="0"
                step="0.01"
                value={formData.security_deposit}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="مبلغ التأمين"
              />
            </div>
          </div>

          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              حالة العقد
            </label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="active">نشط</option>
              <option value="expired">منتهي</option>
              <option value="terminated">مُنهى</option>
              <option value="pending">معلق</option>
            </select>
          </div>

          <div>
            <label htmlFor="contract_terms" className="block text-sm font-medium text-gray-700 mb-1">
              شروط العقد
            </label>
            <textarea
              id="contract_terms"
              name="contract_terms"
              rows={4}
              value={formData.contract_terms}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="أدخل شروط العقد والملاحظات الإضافية (اختياري)"
            />
          </div>

          <div className="flex space-x-3 space-x-reverse pt-4">
            <button
              type="submit"
              disabled={loading}
              className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50"
            >
              {loading ? 'جاري الحفظ...' : rental ? 'تحديث العقد' : 'إنشاء العقد'}
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="bg-gray-200 text-gray-800 px-6 py-2 rounded-lg hover:bg-gray-300 transition-colors"
            >
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
