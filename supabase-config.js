// إعداد Supabase
const SUPABASE_URL = 'https://pbzwijpjkggeknomvkzr.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiendpanBqa2dnZWtub212a3pyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTY4NjI4NjUsImV4cCI6MjAzMjQzODg2NX0.Ej8rNkLjqZixFEeVSQjlOLEVtlhHXWJvPHdJQGJQKQs';

// إنشاء عميل Supabase
const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// متغيرات عامة
let currentUser = null;
let currentSession = null;

// ===== وظائف المصادقة مع Supabase =====

// تسجيل دخول جديد
async function signUpWithSupabase(email, password, name, company) {
    try {
        // إنشاء حساب في Supabase Auth
        const { data: authData, error: authError } = await supabase.auth.signUp({
            email: email,
            password: password,
        });

        if (authError) {
            throw authError;
        }

        // إضافة بيانات المستخدم في جدول users
        if (authData.user) {
            const { error: profileError } = await supabase
                .from('users')
                .insert([
                    {
                        id: authData.user.id,
                        email: email,
                        name: name,
                        company: company
                    }
                ]);

            if (profileError) {
                console.error('Error creating user profile:', profileError);
            }
        }

        return { data: authData, error: null };
    } catch (error) {
        return { data: null, error: error };
    }
}

// تسجيل الدخول
async function signInWithSupabase(email, password) {
    try {
        const { data, error } = await supabase.auth.signInWithPassword({
            email: email,
            password: password,
        });

        if (error) {
            throw error;
        }

        // جلب بيانات المستخدم من جدول users
        if (data.user) {
            const { data: userData, error: userError } = await supabase
                .from('users')
                .select('*')
                .eq('id', data.user.id)
                .single();

            if (!userError && userData) {
                currentUser = userData;
                currentSession = data.session;
            }
        }

        return { data: data, error: null };
    } catch (error) {
        return { data: null, error: error };
    }
}

// تسجيل الخروج
async function signOutFromSupabase() {
    try {
        const { error } = await supabase.auth.signOut();
        currentUser = null;
        currentSession = null;
        return { error: null };
    } catch (error) {
        return { error: error };
    }
}

// التحقق من الجلسة الحالية
async function getCurrentSession() {
    try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
            throw error;
        }

        if (session) {
            // جلب بيانات المستخدم
            const { data: userData, error: userError } = await supabase
                .from('users')
                .select('*')
                .eq('id', session.user.id)
                .single();

            if (!userError && userData) {
                currentUser = userData;
                currentSession = session;
                return { session: session, user: userData };
            }
        }

        return { session: null, user: null };
    } catch (error) {
        console.error('Error getting session:', error);
        return { session: null, user: null };
    }
}

// مراقبة تغييرات المصادقة
supabase.auth.onAuthStateChange(async (event, session) => {
    if (event === 'SIGNED_IN' && session) {
        // جلب بيانات المستخدم عند تسجيل الدخول
        const { data: userData } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single();

        if (userData) {
            currentUser = userData;
            currentSession = session;
        }
    } else if (event === 'SIGNED_OUT') {
        currentUser = null;
        currentSession = null;
    }
});

// ===== وظائف قاعدة البيانات =====

// العقارات
async function getProperties() {
    if (!currentUser) return { data: [], error: 'User not authenticated' };
    
    const { data, error } = await supabase
        .from('properties')
        .select('*')
        .eq('user_id', currentUser.id)
        .order('created_at', { ascending: false });
    
    return { data: data || [], error };
}

async function addProperty(propertyData) {
    if (!currentUser) return { data: null, error: 'User not authenticated' };
    
    const { data, error } = await supabase
        .from('properties')
        .insert([{ ...propertyData, user_id: currentUser.id }])
        .select()
        .single();
    
    return { data, error };
}

async function updateProperty(id, propertyData) {
    if (!currentUser) return { data: null, error: 'User not authenticated' };
    
    const { data, error } = await supabase
        .from('properties')
        .update(propertyData)
        .eq('id', id)
        .eq('user_id', currentUser.id)
        .select()
        .single();
    
    return { data, error };
}

async function deleteProperty(id) {
    if (!currentUser) return { data: null, error: 'User not authenticated' };
    
    const { error } = await supabase
        .from('properties')
        .delete()
        .eq('id', id)
        .eq('user_id', currentUser.id);
    
    return { error };
}

// المستأجرين
async function getTenants() {
    if (!currentUser) return { data: [], error: 'User not authenticated' };
    
    const { data, error } = await supabase
        .from('tenants')
        .select('*')
        .eq('user_id', currentUser.id)
        .order('created_at', { ascending: false });
    
    return { data: data || [], error };
}

async function addTenant(tenantData) {
    if (!currentUser) return { data: null, error: 'User not authenticated' };
    
    const { data, error } = await supabase
        .from('tenants')
        .insert([{ ...tenantData, user_id: currentUser.id }])
        .select()
        .single();
    
    return { data, error };
}

async function deleteTenant(id) {
    if (!currentUser) return { data: null, error: 'User not authenticated' };
    
    const { error } = await supabase
        .from('tenants')
        .delete()
        .eq('id', id)
        .eq('user_id', currentUser.id);
    
    return { error };
}

// عقود الإيجار
async function getRentals() {
    if (!currentUser) return { data: [], error: 'User not authenticated' };
    
    const { data, error } = await supabase
        .from('rental_agreements')
        .select(`
            *,
            property:properties(name, address),
            tenant:tenants(name, phone)
        `)
        .eq('user_id', currentUser.id)
        .order('created_at', { ascending: false });
    
    return { data: data || [], error };
}

async function addRental(rentalData) {
    if (!currentUser) return { data: null, error: 'User not authenticated' };
    
    const { data, error } = await supabase
        .from('rental_agreements')
        .insert([{ ...rentalData, user_id: currentUser.id }])
        .select()
        .single();
    
    return { data, error };
}

async function deleteRental(id) {
    if (!currentUser) return { data: null, error: 'User not authenticated' };
    
    const { error } = await supabase
        .from('rental_agreements')
        .delete()
        .eq('id', id)
        .eq('user_id', currentUser.id);
    
    return { error };
}

// المدفوعات
async function getPayments() {
    if (!currentUser) return { data: [], error: 'User not authenticated' };
    
    const { data, error } = await supabase
        .from('payments')
        .select(`
            *,
            rental_agreement:rental_agreements(
                monthly_rent,
                property:properties(name, address),
                tenant:tenants(name, phone)
            )
        `)
        .eq('user_id', currentUser.id)
        .order('due_date', { ascending: true });
    
    return { data: data || [], error };
}

async function addPayment(paymentData) {
    if (!currentUser) return { data: null, error: 'User not authenticated' };
    
    const { data, error } = await supabase
        .from('payments')
        .insert([{ ...paymentData, user_id: currentUser.id }])
        .select()
        .single();
    
    return { data, error };
}

async function deletePayment(id) {
    if (!currentUser) return { data: null, error: 'User not authenticated' };
    
    const { error } = await supabase
        .from('payments')
        .delete()
        .eq('id', id)
        .eq('user_id', currentUser.id);
    
    return { error };
}

// المصروفات
async function getExpenses() {
    if (!currentUser) return { data: [], error: 'User not authenticated' };
    
    const { data, error } = await supabase
        .from('expenses')
        .select(`
            *,
            property:properties(name, address)
        `)
        .eq('user_id', currentUser.id)
        .order('expense_date', { ascending: false });
    
    return { data: data || [], error };
}

async function addExpense(expenseData) {
    if (!currentUser) return { data: null, error: 'User not authenticated' };
    
    const { data, error } = await supabase
        .from('expenses')
        .insert([{ ...expenseData, user_id: currentUser.id }])
        .select()
        .single();
    
    return { data, error };
}

async function deleteExpense(id) {
    if (!currentUser) return { data: null, error: 'User not authenticated' };
    
    const { error } = await supabase
        .from('expenses')
        .delete()
        .eq('id', id)
        .eq('user_id', currentUser.id);
    
    return { error };
}
